"use client";

import React, { useState, useEffect } from "react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Bar } from "react-chartjs-2";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>er,
  <PERSON>Title,
} from "@/components/ui/layout";
import { Button } from "@/components/ui/forms";
import { Badge } from "@/components/ui/feedback";
import { Progress } from "@/components/ui/feedback";
import {
  Loader2,
  Users,
  Target,
  AlertCircle,
  CheckCircle,
  BookOpen,
  Award,
  BarChart3,
  Eye,
  ChevronDown,
  ChevronUp,
  TrendingUp,
  Clock,
  User,
} from "lucide-react";
import { chapterAnalyticsService } from "@/lib/services/api/chapter-analytics.service";
import { showErrorToast } from "@/lib/utils/toast-utils";

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface StudentGroupBarChartProps {
  quizId: number;
  className?: string;
}

// API Response Types based on spec
interface ChartData {
  group_name: string;
  display_name: string;
  student_count: number;
  percentage: number;
  score_range: { min: number; max: number; average: number };
  color: string;
}

interface StudentGroupsChartResponse {
  chart_data: ChartData[];
  total_students: number;
  chart_config: {
    x_axis: string;
    y_axis: string;
    tooltip_fields: string[];
    clickable: boolean;
  };
}

interface StudentDetail {
  user_id: number;
  name: string;
  email: string;
  score: number;
  percentage_score: number;
  completion_time: number;
  average_time_per_question: number;
  total_questions_attempted: number;
  correct_answers: number;
}

interface StudentLOAnalysis {
  student_info: {
    user_id: number;
    name: string;
    email: string;
    quiz_score: number;
    completion_time: number;
    overall_percentage: number;
  };
  quiz_info: {
    quiz_id: number;
    name: string;
    total_questions: number;
  };
  lo_analysis: Array<{
    lo_id: number;
    lo_name: string;
    lo_description: string;
    total_questions: number;
    attempted_questions: number;
    correct_answers: number;
    achievement_percentage: number;
    performance_level: string;
    status: string;
    color: string;
    average_time_seconds: number;
    total_time_seconds: number;
    insights: string[];
    recommendations: string[];
  }>;
  summary: {
    total_los: number;
    strengths_count: number;
    weaknesses_count: number;
    needs_improvement_count: number;
    strongest_lo: { lo_name: string; percentage: number };
    weakest_lo: { lo_name: string; percentage: number };
  };
}

interface GroupDetailResponse {
  group_info: {
    group_name: string;
    display_name: string;
    student_count: number;
    average_score: number;
    average_percentage: number;
    threshold: number;
  };
  students: StudentDetail[];
  insights: string[];
  recommendations: Array<{
    type: string;
    suggestion: string;
    priority: string;
  }>;
}

export default function StudentGroupBarChart({
  quizId,
  className = "",
}: StudentGroupBarChartProps) {
  const [chartData, setChartData] = useState<StudentGroupsChartResponse | null>(
    null
  );
  const [selectedGroup, setSelectedGroup] = useState<string | null>(null);
  const [selectedStudent, setSelectedStudent] = useState<StudentDetail | null>(
    null
  );
  const [groupDetailData, setGroupDetailData] =
    useState<GroupDetailResponse | null>(null);
  const [studentLOData, setStudentLOData] = useState<StudentLOAnalysis | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [groupLoading, setGroupLoading] = useState(false);
  const [studentLOLoading, setStudentLOLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch chart data for bar chart
  const fetchChartData = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await chapterAnalyticsService.getStudentGroupsChart(
        quizId
      );

      console.log("Chart data for bar chart:", result);
      setChartData(result);
    } catch (err) {
      console.error("Error fetching chart data:", err);
      const errorMessage =
        err instanceof Error ? err.message : "Không thể tải dữ liệu";
      setError(errorMessage);
      showErrorToast("Không thể tải dữ liệu biểu đồ");
    } finally {
      setLoading(false);
    }
  };

  // Fetch group detail data when clicking on a bar
  const fetchGroupDetail = async (groupName: string) => {
    try {
      setGroupLoading(true);
      const result = await chapterAnalyticsService.getStudentGroupAnalysis(
        quizId,
        groupName
      );

      console.log("Group detail data:", result);
      setGroupDetailData(result);
    } catch (err) {
      console.error("Error fetching group detail:", err);
      showErrorToast("Không thể tải chi tiết nhóm");
    } finally {
      setGroupLoading(false);
    }
  };

  // Fetch student LO analysis when clicking on a student
  const fetchStudentLOAnalysis = async (userId: number) => {
    try {
      setStudentLOLoading(true);
      const result = await chapterAnalyticsService.getStudentLOAnalysis(
        quizId,
        userId
      );

      console.log("Student LO analysis data:", result);
      setStudentLOData(result);
    } catch (err) {
      console.error("Error fetching student LO analysis:", err);
      showErrorToast("Không thể tải chi tiết Learning Outcomes");
    } finally {
      setStudentLOLoading(false);
    }
  };

  useEffect(() => {
    if (quizId) {
      fetchChartData();
    }
  }, [quizId]);

  // Handle bar click
  const handleBarClick = (groupName: string) => {
    setSelectedGroup(groupName);
    setSelectedStudent(null); // Reset selected student
    setStudentLOData(null); // Reset LO data
    fetchGroupDetail(groupName);
  };

  // Handle student click
  const handleStudentClick = (student: StudentDetail) => {
    if (selectedStudent?.user_id === student.user_id) {
      // If clicking the same student, collapse
      setSelectedStudent(null);
      setStudentLOData(null);
    } else {
      // If clicking a different student, expand and fetch LO data
      setSelectedStudent(student);
      setStudentLOData(null); // Reset previous LO data
      fetchStudentLOAnalysis(student.user_id);
    }
  };

  // Helper functions
  const getGroupColor = (groupName: string) => {
    switch (groupName) {
      case "excellent":
        return "#4CAF50";
      case "good":
        return "#2196F3";
      case "average":
        return "#FF9800";
      case "weak":
        return "#F44336";
      default:
        return "#9E9E9E";
    }
  };

  const getGroupLabel = (groupName: string) => {
    switch (groupName) {
      case "excellent":
        return "Xuất sắc";
      case "good":
        return "Khá";
      case "average":
        return "Trung bình";
      case "weak":
        return "Yếu";
      default:
        return groupName;
    }
  };

  const getPerformanceColor = (level: string) => {
    switch (level) {
      case "excellent":
        return "text-green-600 bg-green-100";
      case "good":
        return "text-blue-600 bg-blue-100";
      case "average":
        return "text-yellow-600 bg-yellow-100";
      case "weak":
        return "text-red-600 bg-red-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  // Get chart data from API response
  const groups = chartData?.chart_data || [];

  // Prepare Chart.js data
  const chartJSData = {
    labels: groups.map((group) => group.display_name),
    datasets: [
      {
        label: "Số lượng học sinh",
        data: groups.map((group) => group.student_count || 0),
        backgroundColor: groups.map((group) => group.color),
        borderColor: groups.map((group) => group.color),
        borderWidth: 1,
        borderRadius: 4,
        borderSkipped: false,
      },
    ],
  };

  // Chart.js options with click handler
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false, // Hide legend since we have our own
      },
      tooltip: {
        callbacks: {
          label: function (context: any) {
            const group = groups[context.dataIndex];
            return [
              `Số lượng: ${context.parsed.y} học sinh`,
              `Tỷ lệ: ${(group.percentage || 0).toFixed(1)}%`,
              `Điểm TB: ${group.score_range?.average?.toFixed(1) || "N/A"}`,
            ];
          },
        },
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          stepSize: 1,
        },
        title: {
          display: true,
          text: "Số lượng học sinh",
        },
      },
      x: {
        title: {
          display: true,
          text: "Nhóm học sinh",
        },
      },
    },
    onClick: (event: any, elements: any) => {
      if (elements.length > 0) {
        const clickedIndex = elements[0].index;
        const clickedGroup = groups[clickedIndex];
        if (clickedGroup) {
          handleBarClick(clickedGroup.group_name);
        }
      }
    },
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-3 mx-auto" />
            <p className="text-muted-foreground">Đang tải dữ liệu biểu đồ...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="flex flex-col items-center py-8">
          <AlertCircle className="h-16 w-16 text-red-500 mb-4" />
          <p className="text-lg font-medium text-red-600 mb-2">
            Lỗi tải dữ liệu
          </p>
          <p className="text-muted-foreground text-center mb-4">{error}</p>
          <Button onClick={fetchChartData} variant="outline">
            Thử lại
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Bar Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-6 w-6 text-primary" />
            Phân bố nhóm học sinh
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Click vào cột để xem chi tiết nhóm
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Chart.js Bar Chart */}
            <div className="h-64">
              <Bar data={chartJSData} options={chartOptions} />
            </div>

            {/* Legend with Selection Indicator */}
            <div className="flex flex-wrap justify-center gap-4 pt-4 border-t">
              {groups.map((group) => (
                <div
                  key={group.group_name}
                  className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all cursor-pointer ${
                    selectedGroup === group.group_name
                      ? "bg-primary/10 ring-2 ring-primary/20"
                      : "hover:bg-gray-50"
                  }`}
                  onClick={() => handleBarClick(group.group_name)}
                >
                  <div
                    className="w-4 h-4 rounded"
                    style={{ backgroundColor: group.color }}
                  />
                  <span
                    className={`text-sm ${
                      selectedGroup === group.group_name
                        ? "font-medium text-primary"
                        : ""
                    }`}
                  >
                    {group.display_name}: {group.student_count || 0} học sinh
                  </span>
                  {selectedGroup === group.group_name && (
                    <CheckCircle className="h-4 w-4 text-primary" />
                  )}
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Selected Group Details */}
      {selectedGroup && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-6 w-6 text-primary" />
              Chi tiết nhóm {getGroupLabel(selectedGroup)}
              {groupLoading && <Loader2 className="h-4 w-4 animate-spin" />}
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              Click vào tên học sinh để xem chi tiết Learning Outcomes
            </p>
          </CardHeader>
          <CardContent>
            {groupLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-center">
                  <Loader2 className="h-8 w-8 animate-spin text-primary mb-3 mx-auto" />
                  <p className="text-muted-foreground">
                    Đang tải danh sách học sinh...
                  </p>
                </div>
              </div>
            ) : groupDetailData?.students &&
              groupDetailData.students.length > 0 ? (
              <div className="space-y-3">
                {Array.isArray(groupDetailData.students) ? (
                  groupDetailData.students.map(
                    (student: any, index: number) => (
                      <div key={student.user_id} className="border rounded-lg">
                        {/* Student Header - Clickable */}
                        <div
                          className="flex items-center justify-between p-4 cursor-pointer hover:bg-muted/50 transition-colors"
                          onClick={() => handleStudentClick(student)}
                        >
                          <div className="flex items-center gap-3">
                            <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center text-sm font-medium">
                              {index + 1}
                            </div>
                            <div>
                              <p className="font-medium">
                                {student?.name || "Không có tên"}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                ID: {student?.user_id || "N/A"}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-4">
                            <div className="text-right">
                              <div className="text-lg font-bold text-primary">
                                {student?.score?.toFixed(1) || "0.0"}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {student?.correct_answers || 0}/
                                {student?.total_questions_attempted || 0} câu
                              </div>
                            </div>
                            <div className="text-muted-foreground">
                              {selectedStudent?.user_id === student.user_id ? (
                                <ChevronUp className="h-4 w-4" />
                              ) : (
                                <ChevronDown className="h-4 w-4" />
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Student Details - Expandable */}
                        {selectedStudent?.user_id === student.user_id && (
                          <div className="border-t bg-muted/20 p-4">
                            <div className="space-y-4">
                              {/* Performance Summary */}
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-3 bg-white rounded-lg">
                                <div className="text-center">
                                  <div className="text-lg font-semibold text-blue-600">
                                    {student?.score?.toFixed(1) || "0.0"}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    Điểm số
                                  </div>
                                </div>
                                <div className="text-center">
                                  <div className="text-lg font-semibold text-green-600">
                                    {(student?.percentage_score || 0).toFixed(
                                      1
                                    )}
                                    %
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    Độ chính xác
                                  </div>
                                </div>
                                <div className="text-center">
                                  <div className="text-lg font-semibold text-purple-600">
                                    {student?.correct_answers || 0}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    Câu đúng
                                  </div>
                                </div>
                                <div className="text-center">
                                  <div className="text-lg font-semibold text-orange-600">
                                    {student?.total_questions_attempted || 0}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    Tổng câu
                                  </div>
                                </div>
                              </div>

                              {/* Learning Outcomes Details */}
                              {studentLOLoading ? (
                                <div className="flex items-center justify-center py-4">
                                  <Loader2 className="h-6 w-6 animate-spin text-primary mr-2" />
                                  <span className="text-sm text-muted-foreground">
                                    Đang tải chi tiết Learning Outcomes...
                                  </span>
                                </div>
                              ) : studentLOData?.lo_analysis &&
                                studentLOData.lo_analysis.length > 0 ? (
                                <div>
                                  <h4 className="font-semibold mb-3 flex items-center gap-2">
                                    <BookOpen className="h-4 w-4" />
                                    Chi tiết Learning Outcomes
                                  </h4>
                                  <div className="space-y-3">
                                    {studentLOData.lo_analysis.map(
                                      (lo, loIndex) => (
                                        <div
                                          key={loIndex}
                                          className="bg-white border rounded-lg p-3"
                                        >
                                          <div className="flex items-center justify-between mb-2">
                                            <div className="flex-1">
                                              <h5 className="font-medium text-sm">
                                                {lo.lo_name}
                                              </h5>
                                              <p className="text-xs text-muted-foreground mb-2">
                                                {lo.lo_description}
                                              </p>
                                              <div className="flex items-center gap-2 mt-1">
                                                <Badge
                                                  className={getPerformanceColor(
                                                    lo.performance_level
                                                  )}
                                                  style={{
                                                    backgroundColor: lo.color,
                                                    color: "white",
                                                  }}
                                                >
                                                  {lo.status}
                                                </Badge>
                                                <span className="text-xs text-muted-foreground">
                                                  {lo.correct_answers}/
                                                  {lo.total_questions} câu đúng
                                                </span>
                                              </div>
                                            </div>
                                            <div className="text-right">
                                              <div className="text-lg font-bold">
                                                {lo.achievement_percentage.toFixed(
                                                  1
                                                )}
                                                %
                                              </div>
                                              <Progress
                                                value={
                                                  lo.achievement_percentage
                                                }
                                                className="w-16 h-2 mt-1"
                                              />
                                            </div>
                                          </div>

                                          {/* Insights and Recommendations */}
                                          {(lo.insights.length > 0 ||
                                            lo.recommendations.length > 0) && (
                                            <div className="mt-3 pt-3 border-t">
                                              {lo.insights.length > 0 && (
                                                <div className="mb-2">
                                                  <p className="text-xs font-medium text-blue-600 mb-1">
                                                    Nhận xét:
                                                  </p>
                                                  {lo.insights.map(
                                                    (insight, idx) => (
                                                      <p
                                                        key={idx}
                                                        className="text-xs text-muted-foreground"
                                                      >
                                                        • {insight}
                                                      </p>
                                                    )
                                                  )}
                                                </div>
                                              )}
                                              {lo.recommendations.length >
                                                0 && (
                                                <div>
                                                  <p className="text-xs font-medium text-green-600 mb-1">
                                                    Khuyến nghị:
                                                  </p>
                                                  {lo.recommendations.map(
                                                    (rec, idx) => (
                                                      <p
                                                        key={idx}
                                                        className="text-xs text-muted-foreground"
                                                      >
                                                        • {rec}
                                                      </p>
                                                    )
                                                  )}
                                                </div>
                                              )}
                                            </div>
                                          )}
                                        </div>
                                      )
                                    )}
                                  </div>
                                </div>
                              ) : (
                                selectedStudent &&
                                !studentLOLoading && (
                                  <div className="flex flex-col items-center justify-center py-4 text-center">
                                    <BookOpen className="h-8 w-8 text-muted-foreground mb-2" />
                                    <p className="text-sm text-muted-foreground">
                                      Không có dữ liệu Learning Outcomes
                                    </p>
                                  </div>
                                )
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    )
                  )
                ) : (
                  <div className="flex flex-col items-center justify-center py-8 text-center">
                    <Users className="h-12 w-12 text-muted-foreground mb-3" />
                    <p className="text-muted-foreground">
                      Không có sinh viên trong nhóm này
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <Users className="h-12 w-12 text-muted-foreground mb-3" />
                <p className="text-muted-foreground">
                  Không có dữ liệu sinh viên
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
