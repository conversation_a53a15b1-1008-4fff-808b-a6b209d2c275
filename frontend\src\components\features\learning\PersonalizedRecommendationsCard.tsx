"use client";

import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@/components/ui/layout";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  Ta<PERSON>Trigger,
} from "@/components/ui/navigation";
import { Badge, Progress } from "@/components/ui/feedback";
import { Button } from "@/components/ui/forms";
import {
  Target,
  Clock,
  TrendingUp,
  Calendar,
  CheckCircle,
  AlertCircle,
  Lightbulb,
  ArrowRight,
  Star,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  PersonalizedRecommendationsProps,
  ImmediateFocusAction,
  NextPhaseItem,
  DailyBreakdown,
  MilestoneTarget,
} from "@/lib/types/lo-completion-analysis";

export function PersonalizedRecommendationsCard({
  className,
  recommendations,
  onActionClick,
  onPhaseSelect,
}: PersonalizedRecommendationsProps) {
  // Log recommendations for debugging (can be removed in production)
  React.useEffect(() => {
    if (recommendations) {
      console.log("PersonalizedRecommendationsCard received data:", recommendations);
    }
  }, [recommendations]);

  const [activeTab, setActiveTab] = useState("immediate");

  const getPriorityColor = (priority: "high" | "medium" | "low") => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800 border-red-200";
      case "medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "low":
        return "bg-green-100 text-green-800 border-green-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getPriorityIcon = (priority: "high" | "medium" | "low") => {
    switch (priority) {
      case "high":
        return <AlertCircle className="h-3 w-3" />;
      case "medium":
        return <Clock className="h-3 w-3" />;
      case "low":
        return <CheckCircle className="h-3 w-3" />;
      default:
        return <Target className="h-3 w-3" />;
    }
  };

  const renderImmediateFocus = () => (
    <div className="space-y-4">
      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
        <h3 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
          <Target className="h-4 w-4" />
          Hành động ưu tiên ngay
        </h3>
        <p className="text-sm text-blue-700">
          Các hành động cần thực hiện ngay lập tức để cải thiện hiệu quả học tập
        </p>
      </div>

      {recommendations.immediate_focus && recommendations.immediate_focus.length > 0 ? (
        <div className="space-y-3">
          {recommendations.immediate_focus.map((action, index) => (
          <Card key={index} className="border-l-4 border-l-red-500">
            <CardContent className="p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 mb-2">{(action as any).lo_name}</h4>
                  <div className="flex items-center gap-2 mb-2">
                    <Badge className="text-xs flex items-center gap-1 bg-red-100 text-red-800">
                      <AlertCircle className="h-3 w-3" />
                      {(action as any).type}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{(action as any).reason}</p>
                </div>
              </div>

              <div className="mb-3">
                <p className="text-sm font-medium text-red-700">{(action as any).action}</p>
              </div>

              <Button
                size="sm"
                onClick={() => onActionClick?.((action as any).action)}
                className="w-full"
              >
                Thực hiện ngay
              </Button>
            </CardContent>
          </Card>
        ))}
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          <Target className="h-12 w-12 mx-auto mb-2 text-gray-400" />
          <p>Hiện tại không có hành động ưu tiên nào</p>
          <p className="text-sm">Tất cả LO đều đã đạt mức thành thạo tốt</p>
        </div>
      )}
    </div>
  );

  const renderNextPhase = () => (
    <div className="space-y-4">
      <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
        <h3 className="font-medium text-purple-900 mb-2 flex items-center gap-2">
          <TrendingUp className="h-4 w-4" />
          Giai đoạn tiếp theo
        </h3>
        <p className="text-sm text-purple-700">
          Lộ trình học tập dài hạn với các giai đoạn phát triển có hệ thống
        </p>
      </div>

      <div className="space-y-4">
        {recommendations.next_phase?.map((phase, index) => (
          <Card key={index} className="border-l-4 border-l-purple-500">
            <CardContent className="p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 mb-1">
                    {phase.type === "advancement" ? "Nâng cao LO" : "Giai đoạn"}: {phase.lo_name}
                  </h4>
                  <p className="text-sm text-gray-600 mb-2">{phase.reason}</p>

                  <div className="flex items-center gap-2 mb-3">
                    <TrendingUp className="h-4 w-4 text-purple-600" />
                    <span className="text-sm text-gray-700 font-medium">
                      {phase.action}
                    </span>
                  </div>
                </div>
                <div className="text-right">
                  <Badge variant="outline" className="text-xs">
                    {phase.type}
                  </Badge>
                </div>
              </div>

              <Button
                size="sm"
                variant="outline"
                onClick={() => onPhaseSelect?.(phase.lo_name)}
                className="w-full"
              >
                Bắt đầu học LO {phase.lo_name}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderStudySchedule = () => (
    <div className="space-y-4">
      <div className="bg-green-50 p-4 rounded-lg border border-green-200">
        <h3 className="font-medium text-green-900 mb-2 flex items-center gap-2">
          <Calendar className="h-4 w-4" />
          Lịch học cá nhân hóa
        </h3>
        <p className="text-sm text-green-700">
          Kế hoạch học tập chi tiết theo tuần với mục tiêu cụ thể
        </p>
      </div>

      {/* Study Schedule Overview */}
      <Card>
        <CardContent className="p-4">
          <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
            <Star className="h-4 w-4 text-yellow-500" />
            Lịch học được đề xuất
          </h4>

          {recommendations.study_schedule && Object.keys(recommendations.study_schedule).length > 0 ? (
            <div className="space-y-3">
              {Object.entries(recommendations.study_schedule).map(([weekKey, weekData], index) => (
                <div key={index} className="bg-green-50 p-3 rounded-lg border border-green-200">
                  <div className="font-medium text-green-900 mb-2">
                    {weekKey.replace('_', ' ').toUpperCase()}
                  </div>

                  {typeof weekData === 'object' && weekData !== null && (
                    <div className="space-y-2">
                      {weekData.focus && (
                        <div className="text-sm text-green-800">
                          <strong>Tập trung:</strong> {weekData.focus}
                        </div>
                      )}

                      {weekData.chapters && Array.isArray(weekData.chapters) && (
                        <div className="text-sm text-green-800">
                          <strong>Chương:</strong> {weekData.chapters.join(", ")}
                        </div>
                      )}

                      {weekData.target_completion && (
                        <div className="text-sm text-green-800">
                          <strong>Mục tiêu:</strong> {weekData.target_completion}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Calendar className="h-12 w-12 mx-auto mb-2 text-gray-400" />
              <p>Chưa có lịch học cụ thể</p>
              <p className="text-sm">Hệ thống sẽ tạo lịch học dựa trên tiến độ hiện tại</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Lightbulb className="h-5 w-5 text-blue-600" />
          Gợi ý học tập cá nhân hóa
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Kế hoạch học tập được tùy chỉnh dựa trên phân tích LO completion của bạn
        </p>
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="immediate" className="flex items-center gap-2 text-sm">
              <Target className="h-4 w-4" />
              Ngay lập tức
            </TabsTrigger>
            <TabsTrigger value="next-phase" className="flex items-center gap-2 text-sm">
              <TrendingUp className="h-4 w-4" />
              Giai đoạn tiếp theo
            </TabsTrigger>
            <TabsTrigger value="schedule" className="flex items-center gap-2 text-sm">
              <Calendar className="h-4 w-4" />
              Lịch học
            </TabsTrigger>
          </TabsList>

          <TabsContent value="immediate" className="mt-6">
            {renderImmediateFocus()}
          </TabsContent>

          <TabsContent value="next-phase" className="mt-6">
            {renderNextPhase()}
          </TabsContent>

          <TabsContent value="schedule" className="mt-6">
            {renderStudySchedule()}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
