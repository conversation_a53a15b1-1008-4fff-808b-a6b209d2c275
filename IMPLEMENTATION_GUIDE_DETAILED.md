# 🚀 QL_CTDT_V2 - Implementation Guide Chi tiết

## 📋 Tổng quan

Document này cung cấp hướng dẫn chi tiết để implement QL_CTDT_V2 với **Hybrid DDD + Event-Driven Architecture**. <PERSON><PERSON><PERSON> là roadmap hoàn chỉnh từ setup project đến deployment production.

## 🏗️ Project Structure

### **Root Directory Setup**
```
QL_CTDT_V2/
├── README.md
├── docker-compose.yml
├── docker-compose.dev.yml
├── docker-compose.prod.yml
├── package.json                    # Root package.json cho workspace
├── pnpm-workspace.yaml            # PNPM workspace config
├── .env.example
├── .gitignore
├── domains/                        # Business domains
│   ├── assessment/
│   ├── practice/
│   ├── content/
│   └── analytics/
├── shared/                         # Shared utilities
│   ├── events/
│   ├── database/
│   ├── auth/
│   ├── middleware/
│   └── utils/
├── gateway/                        # API Gateway & Web Portal
│   ├── api-gateway/
│   ├── web-portal/
│   └── event-bus/
├── infrastructure/                 # Infrastructure as Code
│   ├── docker/
│   ├── nginx/
│   ├── monitoring/
│   └── deployment/
├── docs/                          # Documentation
│   ├── api/
│   ├── architecture/
│   ├── deployment/
│   └── development/
├── scripts/                       # Utility scripts
│   ├── setup.sh
│   ├── dev.sh
│   ├── build.sh
│   ├── deploy.sh
│   └── migrate.sh
└── tests/                         # Integration tests
    ├── e2e/
    ├── load/
    └── integration/
```

## 🎯 Phase 1: Project Foundation (Week 1)

### **Step 1.1: Repository Setup**
```bash
# 1. Create new repository
mkdir QL_CTDT_V2
cd QL_CTDT_V2
git init

# 2. Setup workspace configuration
cat > package.json << 'EOF'
{
  "name": "ql-ctdt-v2",
  "version": "1.0.0",
  "description": "QL_CTDT V2 - Microservice Architecture",
  "private": true,
  "workspaces": [
    "domains/*/services/*",
    "gateway/*",
    "shared/*"
  ],
  "scripts": {
    "dev": "docker-compose -f docker-compose.dev.yml up",
    "build": "pnpm run build --recursive",
    "test": "pnpm run test --recursive",
    "lint": "pnpm run lint --recursive",
    "setup": "./scripts/setup.sh",
    "migrate": "./scripts/migrate.sh"
  },
  "devDependencies": {
    "concurrently": "^7.6.0",
    "nodemon": "^2.0.20",
    "eslint": "^8.0.0",
    "prettier": "^2.8.0"
  }
}
EOF

# 3. Setup PNPM workspace
cat > pnpm-workspace.yaml << 'EOF'
packages:
  - 'domains/*/services/*'
  - 'gateway/*'
  - 'shared/*'
  - 'infrastructure/*'
EOF

# 4. Create directory structure
mkdir -p domains/{assessment,practice,content,analytics}/services
mkdir -p shared/{events,database,auth,middleware,utils}
mkdir -p gateway/{api-gateway,web-portal,event-bus}
mkdir -p infrastructure/{docker,nginx,monitoring,deployment}
mkdir -p docs/{api,architecture,deployment,development}
mkdir -p scripts tests/{e2e,load,integration}
```

### **Step 1.2: Environment Configuration**
```bash
# Create environment files
cat > .env.example << 'EOF'
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres
DB_NAME=ql_ctdt_v2

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=24h

# Service Ports
API_GATEWAY_PORT=8080
WEB_PORTAL_PORT=3000
ASSESSMENT_UI_PORT=3001
PRACTICE_UI_PORT=3002
CONTENT_MGMT_PORT=3003
ANALYTICS_DASHBOARD_PORT=3004

# Assessment Services
ASSESSMENT_QUIZ_ENGINE_PORT=8081
ASSESSMENT_PROCTORING_PORT=8082
ASSESSMENT_GRADING_PORT=8083

# Practice Services
PRACTICE_QUIZ_ENGINE_PORT=8084
PRACTICE_GAMIFICATION_PORT=8085
PRACTICE_CURRENCY_PORT=8086
PRACTICE_SKILLS_PORT=8087
PRACTICE_SOCIAL_PORT=8088

# Content Services
CONTENT_QUIZ_BUILDER_PORT=8089
CONTENT_QUESTION_BANK_PORT=8090
CONTENT_CURRICULUM_PORT=8091
CONTENT_MEDIA_PORT=8092

# Analytics Services
ANALYTICS_DATA_COLLECTION_PORT=8093
ANALYTICS_REPORTING_PORT=8094
ANALYTICS_INSIGHTS_PORT=8095
ANALYTICS_REALTIME_PORT=8096

# Event Bus Configuration
EVENT_BUS_TYPE=redis
KAFKA_BROKERS=localhost:9092
RABBITMQ_URL=amqp://localhost:5672

# External Services
ELASTICSEARCH_URL=http://localhost:9200
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin

# Monitoring
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001
JAEGER_PORT=16686

# Development
NODE_ENV=development
LOG_LEVEL=debug
ENABLE_CORS=true
ENABLE_SWAGGER=true
EOF

# Copy to actual .env file
cp .env.example .env
```

### **Step 1.3: Docker Infrastructure**
```bash
# Create main docker-compose.yml
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  # Infrastructure Services
  postgres:
    image: postgres:15-alpine
    container_name: ql_ctdt_postgres
    environment:
      POSTGRES_DB: ${DB_NAME}
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    ports:
      - "${DB_PORT}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./shared/database/init:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER} -d ${DB_NAME}"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    container_name: ql_ctdt_redis
    ports:
      - "${REDIS_PORT}:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API Gateway
  api-gateway:
    build: ./gateway/api-gateway
    container_name: ql_ctdt_api_gateway
    ports:
      - "${API_GATEWAY_PORT}:8080"
    environment:
      - NODE_ENV=${NODE_ENV}
      - DB_HOST=postgres
      - REDIS_HOST=redis
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Web Portal
  web-portal:
    build: ./gateway/web-portal
    container_name: ql_ctdt_web_portal
    ports:
      - "${WEB_PORTAL_PORT}:3000"
    environment:
      - NODE_ENV=${NODE_ENV}
      - NEXT_PUBLIC_API_URL=http://localhost:${API_GATEWAY_PORT}
    depends_on:
      - api-gateway

  # Event Bus
  event-bus:
    build: ./gateway/event-bus
    container_name: ql_ctdt_event_bus
    environment:
      - NODE_ENV=${NODE_ENV}
      - REDIS_HOST=redis
    depends_on:
      redis:
        condition: service_healthy

volumes:
  postgres_data:
  redis_data:

networks:
  default:
    driver: bridge
EOF

# Create development docker-compose
cat > docker-compose.dev.yml << 'EOF'
version: '3.8'

services:
  postgres:
    extends:
      file: docker-compose.yml
      service: postgres
    ports:
      - "5432:5432"

  redis:
    extends:
      file: docker-compose.yml
      service: redis
    ports:
      - "6379:6379"

  # Development tools
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: ql_ctdt_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    ports:
      - "5050:80"
    depends_on:
      - postgres

  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: ql_ctdt_redis_commander
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis

  # Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: ql_ctdt_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./infrastructure/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana:latest
    container_name: ql_ctdt_grafana
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana

volumes:
  grafana_data:
EOF
```

### **Step 1.4: Setup Scripts**
```bash
# Create setup script
cat > scripts/setup.sh << 'EOF'
#!/bin/bash

echo "🚀 Setting up QL_CTDT_V2 development environment..."

# Check prerequisites
command -v docker >/dev/null 2>&1 || { echo "❌ Docker is required but not installed."; exit 1; }
command -v pnpm >/dev/null 2>&1 || { echo "❌ PNPM is required but not installed."; exit 1; }

# Install dependencies
echo "📦 Installing dependencies..."
pnpm install

# Setup environment
if [ ! -f .env ]; then
    echo "📝 Creating .env file..."
    cp .env.example .env
    echo "✅ Please update .env file with your configuration"
fi

# Start infrastructure
echo "🐳 Starting infrastructure services..."
docker-compose -f docker-compose.dev.yml up -d postgres redis

# Wait for services
echo "⏳ Waiting for services to be ready..."
sleep 10

# Run database migrations
echo "🗄️ Running database migrations..."
./scripts/migrate.sh

echo "✅ Setup completed! Run 'pnpm dev' to start development."
EOF

chmod +x scripts/setup.sh

# Create migration script
cat > scripts/migrate.sh << 'EOF'
#!/bin/bash

echo "🗄️ Running database migrations..."

# Wait for postgres to be ready
until docker exec ql_ctdt_postgres pg_isready -U postgres; do
  echo "Waiting for postgres..."
  sleep 2
done

# Run migrations for each domain
echo "Running shared migrations..."
docker exec ql_ctdt_postgres psql -U postgres -d ql_ctdt_v2 -f /docker-entrypoint-initdb.d/00_shared_schema.sql

echo "Running assessment migrations..."
docker exec ql_ctdt_postgres psql -U postgres -d ql_ctdt_v2 -f /docker-entrypoint-initdb.d/01_assessment_schema.sql

echo "Running practice migrations..."
docker exec ql_ctdt_postgres psql -U postgres -d ql_ctdt_v2 -f /docker-entrypoint-initdb.d/02_practice_schema.sql

echo "Running content migrations..."
docker exec ql_ctdt_postgres psql -U postgres -d ql_ctdt_v2 -f /docker-entrypoint-initdb.d/03_content_schema.sql

echo "Running analytics migrations..."
docker exec ql_ctdt_postgres psql -U postgres -d ql_ctdt_v2 -f /docker-entrypoint-initdb.d/04_analytics_schema.sql

echo "✅ Database migrations completed!"
EOF

chmod +x scripts/migrate.sh

# Create development script
cat > scripts/dev.sh << 'EOF'
#!/bin/bash

echo "🚀 Starting QL_CTDT_V2 development environment..."

# Start infrastructure
docker-compose -f docker-compose.dev.yml up -d

# Start services in development mode
concurrently \
  "cd gateway/api-gateway && pnpm dev" \
  "cd gateway/web-portal && pnpm dev" \
  "cd domains/assessment/services/quiz-engine && pnpm dev" \
  "cd domains/practice/services/quiz-engine && pnpm dev" \
  --names "gateway,portal,assess,practice" \
  --prefix-colors "blue,green,yellow,magenta"
EOF

chmod +x scripts/dev.sh
```

## 🗄️ Phase 2: Database Schema (Week 1)

### **Step 2.1: Shared Database Schema**
```bash
# Create shared schema
cat > shared/database/init/00_shared_schema.sql << 'EOF'
-- =============================================
-- Shared Schema for QL_CTDT_V2
-- =============================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (shared across all domains)
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'student',
    avatar_url VARCHAR(500),
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Roles table
CREATE TABLE roles (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    permissions JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

-- User roles junction table
CREATE TABLE user_roles (
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    role_id INTEGER REFERENCES roles(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP DEFAULT NOW(),
    assigned_by INTEGER REFERENCES users(id),
    PRIMARY KEY (user_id, role_id)
);

-- Organizations/Schools
CREATE TABLE organizations (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) UNIQUE,
    type VARCHAR(50) DEFAULT 'school',
    address TEXT,
    contact_email VARCHAR(255),
    contact_phone VARCHAR(20),
    settings JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- User organization membership
CREATE TABLE user_organizations (
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    organization_id INTEGER REFERENCES organizations(id) ON DELETE CASCADE,
    role VARCHAR(50) DEFAULT 'member',
    joined_at TIMESTAMP DEFAULT NOW(),
    PRIMARY KEY (user_id, organization_id)
);

-- Subjects/Courses
CREATE TABLE subjects (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50),
    description TEXT,
    organization_id INTEGER REFERENCES organizations(id),
    created_by INTEGER REFERENCES users(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Learning Objectives
CREATE TABLE learning_objectives (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    code VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    subject_id INTEGER REFERENCES subjects(id),
    parent_id INTEGER REFERENCES learning_objectives(id),
    level INTEGER DEFAULT 1,
    order_index INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Event store for domain events
CREATE TABLE domain_events (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    event_type VARCHAR(100) NOT NULL,
    aggregate_type VARCHAR(100) NOT NULL,
    aggregate_id VARCHAR(100) NOT NULL,
    event_data JSONB NOT NULL,
    metadata JSONB,
    version INTEGER DEFAULT 1,
    occurred_at TIMESTAMP DEFAULT NOW(),
    processed_at TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_domain_events_type ON domain_events(event_type);
CREATE INDEX idx_domain_events_aggregate ON domain_events(aggregate_type, aggregate_id);
CREATE INDEX idx_domain_events_occurred ON domain_events(occurred_at);

-- Insert default roles
INSERT INTO roles (name, description, permissions) VALUES
('admin', 'System Administrator', '{"all": true}'),
('teacher', 'Teacher/Instructor', '{"content": ["create", "read", "update"], "assessment": ["create", "read", "monitor"], "analytics": ["read"]}'),
('student', 'Student', '{"assessment": ["take"], "practice": ["access"], "profile": ["read", "update"]}'),
('content_manager', 'Content Manager', '{"content": ["create", "read", "update", "delete"], "analytics": ["read"]}');
EOF
```

### **Step 2.2: Assessment Domain Schema**
```bash
cat > shared/database/init/01_assessment_schema.sql << 'EOF'
-- =============================================
-- Assessment Domain Schema
-- =============================================

-- Quizzes for assessment
CREATE TABLE assessment_quizzes (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    subject_id INTEGER REFERENCES subjects(id),
    created_by INTEGER REFERENCES users(id),
    
    -- Assessment specific settings
    time_limit INTEGER, -- minutes
    max_attempts INTEGER DEFAULT 1,
    randomize_questions BOOLEAN DEFAULT false,
    randomize_answers BOOLEAN DEFAULT false,
    show_results BOOLEAN DEFAULT true,
    allow_review BOOLEAN DEFAULT false,
    
    -- Proctoring settings
    enable_proctoring BOOLEAN DEFAULT false,
    require_camera BOOLEAN DEFAULT false,
    require_microphone BOOLEAN DEFAULT false,
    detect_tab_switch BOOLEAN DEFAULT true,
    detect_copy_paste BOOLEAN DEFAULT true,
    
    -- Scheduling
    available_from TIMESTAMP,
    available_until TIMESTAMP,
    
    -- Status
    status VARCHAR(20) DEFAULT 'draft', -- draft, published, archived
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Questions for assessment quizzes
CREATE TABLE assessment_questions (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    quiz_id INTEGER REFERENCES assessment_quizzes(id) ON DELETE CASCADE,
    
    -- Question content
    question_text TEXT NOT NULL,
    question_type VARCHAR(50) NOT NULL, -- multiple_choice, essay, code, etc.
    options JSONB, -- For multiple choice, etc.
    correct_answer JSONB,
    explanation TEXT,
    
    -- Metadata
    points DECIMAL(5,2) DEFAULT 1.0,
    difficulty VARCHAR(20) DEFAULT 'medium',
    learning_objective_id INTEGER REFERENCES learning_objectives(id),
    tags JSONB,
    
    -- Ordering
    order_index INTEGER DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Assessment sessions
CREATE TABLE assessment_sessions (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    quiz_id INTEGER REFERENCES assessment_quizzes(id),
    user_id INTEGER REFERENCES users(id),
    
    -- Session details
    pin VARCHAR(6), -- For joining via PIN
    attempt_number INTEGER DEFAULT 1,
    
    -- Timing
    started_at TIMESTAMP DEFAULT NOW(),
    submitted_at TIMESTAMP,
    time_spent INTEGER, -- seconds
    
    -- Status
    status VARCHAR(20) DEFAULT 'in_progress', -- in_progress, submitted, graded
    
    -- Proctoring data
    proctoring_enabled BOOLEAN DEFAULT false,
    violations JSONB,
    
    created_at TIMESTAMP DEFAULT NOW()
);

-- Student answers
CREATE TABLE assessment_answers (
    id SERIAL PRIMARY KEY,
    session_id INTEGER REFERENCES assessment_sessions(id) ON DELETE CASCADE,
    question_id INTEGER REFERENCES assessment_questions(id),
    
    -- Answer data
    answer_data JSONB NOT NULL,
    is_correct BOOLEAN,
    points_earned DECIMAL(5,2) DEFAULT 0,
    
    -- Timing
    time_spent INTEGER, -- seconds on this question
    answered_at TIMESTAMP DEFAULT NOW(),
    
    -- Metadata
    attempt_count INTEGER DEFAULT 1,
    
    UNIQUE(session_id, question_id)
);

-- Assessment results
CREATE TABLE assessment_results (
    id SERIAL PRIMARY KEY,
    session_id INTEGER REFERENCES assessment_sessions(id) ON DELETE CASCADE,
    
    -- Scoring
    total_points DECIMAL(8,2) NOT NULL,
    max_points DECIMAL(8,2) NOT NULL,
    percentage DECIMAL(5,2) NOT NULL,
    grade VARCHAR(5),
    
    -- Timing
    total_time INTEGER, -- seconds
    
    -- Feedback
    feedback TEXT,
    
    -- Status
    is_final BOOLEAN DEFAULT false,
    graded_by INTEGER REFERENCES users(id),
    graded_at TIMESTAMP,
    
    created_at TIMESTAMP DEFAULT NOW()
);

-- Proctoring logs
CREATE TABLE proctoring_logs (
    id SERIAL PRIMARY KEY,
    session_id INTEGER REFERENCES assessment_sessions(id) ON DELETE CASCADE,
    
    -- Event details
    event_type VARCHAR(50) NOT NULL, -- tab_switch, copy_paste, camera_off, etc.
    event_data JSONB,
    severity VARCHAR(20) DEFAULT 'low', -- low, medium, high, critical
    
    -- Timing
    occurred_at TIMESTAMP DEFAULT NOW(),
    
    -- Review
    reviewed BOOLEAN DEFAULT false,
    reviewed_by INTEGER REFERENCES users(id),
    reviewed_at TIMESTAMP,
    action_taken VARCHAR(100)
);

-- Indexes
CREATE INDEX idx_assessment_quizzes_subject ON assessment_quizzes(subject_id);
CREATE INDEX idx_assessment_quizzes_status ON assessment_quizzes(status);
CREATE INDEX idx_assessment_sessions_quiz ON assessment_sessions(quiz_id);
CREATE INDEX idx_assessment_sessions_user ON assessment_sessions(user_id);
CREATE INDEX idx_assessment_sessions_status ON assessment_sessions(status);
CREATE INDEX idx_assessment_answers_session ON assessment_answers(session_id);
CREATE INDEX idx_proctoring_logs_session ON proctoring_logs(session_id);
CREATE INDEX idx_proctoring_logs_type ON proctoring_logs(event_type);
EOF
```

### **Step 2.3: Practice Domain Schema**
```bash
cat > shared/database/init/02_practice_schema.sql << 'EOF'
-- =============================================
-- Practice Domain Schema
-- =============================================

-- Practice quizzes (gamified)
CREATE TABLE practice_quizzes (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    subject_id INTEGER REFERENCES subjects(id),
    created_by INTEGER REFERENCES users(id),
    
    -- Practice specific settings
    adaptive_difficulty BOOLEAN DEFAULT true,
    allow_hints BOOLEAN DEFAULT true,
    allow_explanations BOOLEAN DEFAULT true,
    allow_retries BOOLEAN DEFAULT true,
    max_retries INTEGER DEFAULT 3,
    
    -- Gamification settings
    xp_reward INTEGER DEFAULT 10,
    coin_reward INTEGER DEFAULT 5,
    gem_reward INTEGER DEFAULT 1,
    
    -- Status
    status VARCHAR(20) DEFAULT 'active',
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Practice questions (with gamification features)
CREATE TABLE practice_questions (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    quiz_id INTEGER REFERENCES practice_quizzes(id) ON DELETE CASCADE,
    
    -- Question content
    question_text TEXT NOT NULL,
    question_type VARCHAR(50) NOT NULL,
    options JSONB,
    correct_answer JSONB,
    explanation TEXT,
    hint TEXT,
    
    -- Gamification
    base_xp INTEGER DEFAULT 10,
    base_coins INTEGER DEFAULT 5,
    difficulty_multiplier DECIMAL(3,2) DEFAULT 1.0,
    
    -- Metadata
    difficulty VARCHAR(20) DEFAULT 'medium',
    learning_objective_id INTEGER REFERENCES learning_objectives(id),
    tags JSONB,
    
    -- Statistics
    total_attempts INTEGER DEFAULT 0,
    correct_attempts INTEGER DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Practice sessions
CREATE TABLE practice_sessions (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    quiz_id INTEGER REFERENCES practice_quizzes(id),
    user_id INTEGER REFERENCES users(id),
    
    -- Session type
    session_type VARCHAR(50) DEFAULT 'normal', -- normal, challenge, competition
    
    -- Progress
    questions_answered INTEGER DEFAULT 0,
    correct_answers INTEGER DEFAULT 0,
    
    -- Rewards earned
    xp_earned INTEGER DEFAULT 0,
    coins_earned INTEGER DEFAULT 0,
    gems_earned INTEGER DEFAULT 0,
    
    -- Timing
    started_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP,
    total_time INTEGER, -- seconds
    
    -- Status
    status VARCHAR(20) DEFAULT 'active', -- active, completed, abandoned
    
    created_at TIMESTAMP DEFAULT NOW()
);

-- User currency
CREATE TABLE user_currency (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE UNIQUE,
    
    -- Currency amounts
    coins INTEGER DEFAULT 0 CHECK (coins >= 0),
    gems INTEGER DEFAULT 0 CHECK (gems >= 0),
    
    -- Lifetime stats
    total_coins_earned INTEGER DEFAULT 0,
    total_gems_earned INTEGER DEFAULT 0,
    total_coins_spent INTEGER DEFAULT 0,
    total_gems_spent INTEGER DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Currency transactions
CREATE TABLE currency_transactions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    
    -- Transaction details
    transaction_type VARCHAR(20) NOT NULL, -- earn, spend, admin_adjust
    currency_type VARCHAR(10) NOT NULL, -- coins, gems
    amount INTEGER NOT NULL, -- positive for earn, negative for spend
    
    -- Balance tracking
    balance_before INTEGER NOT NULL,
    balance_after INTEGER NOT NULL,
    
    -- Source/reason
    source VARCHAR(50) NOT NULL, -- quiz_completion, shop_purchase, daily_bonus, etc.
    source_id VARCHAR(100), -- ID of the source (quiz_id, item_id, etc.)
    description TEXT,
    
    -- Metadata
    metadata JSONB,
    
    created_at TIMESTAMP DEFAULT NOW()
);

-- User XP and levels
CREATE TABLE user_levels (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE UNIQUE,
    
    -- XP and level
    total_xp INTEGER DEFAULT 0,
    current_level INTEGER DEFAULT 1,
    xp_to_next_level INTEGER DEFAULT 100,
    
    -- Subject-specific levels
    subject_levels JSONB DEFAULT '{}',
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Skills system
CREATE TABLE skills (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    
    -- Skill details
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(100),
    
    -- Skill mechanics
    skill_type VARCHAR(50) NOT NULL, -- active, passive
    effect_type VARCHAR(50) NOT NULL, -- time_freeze, double_points, hint_reveal, etc.
    effect_data JSONB NOT NULL,
    
    -- Costs and cooldowns
    coin_cost INTEGER DEFAULT 0,
    gem_cost INTEGER DEFAULT 0,
    cooldown_seconds INTEGER DEFAULT 0,
    
    -- Unlock requirements
    required_level INTEGER DEFAULT 1,
    required_skills JSONB DEFAULT '[]',
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- User skills
CREATE TABLE user_skills (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    skill_id INTEGER REFERENCES skills(id) ON DELETE CASCADE,
    
    -- Skill progress
    level INTEGER DEFAULT 1,
    experience INTEGER DEFAULT 0,
    times_used INTEGER DEFAULT 0,
    
    -- Status
    is_unlocked BOOLEAN DEFAULT false,
    unlocked_at TIMESTAMP,
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    UNIQUE(user_id, skill_id)
);

-- Skill usage history
CREATE TABLE skill_usage_history (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    skill_id INTEGER REFERENCES skills(id),
    session_id INTEGER REFERENCES practice_sessions(id),
    
    -- Usage details
    used_at TIMESTAMP DEFAULT NOW(),
    effect_duration INTEGER, -- seconds
    success BOOLEAN DEFAULT true,
    
    -- Context
    question_id INTEGER,
    context_data JSONB
);

-- Achievements
CREATE TABLE achievements (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    
    -- Achievement details
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(100),
    category VARCHAR(50), -- progress, skill, social, special
    
    -- Requirements
    requirements JSONB NOT NULL,
    
    -- Rewards
    xp_reward INTEGER DEFAULT 0,
    coin_reward INTEGER DEFAULT 0,
    gem_reward INTEGER DEFAULT 0,
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    is_hidden BOOLEAN DEFAULT false,
    
    created_at TIMESTAMP DEFAULT NOW()
);

-- User achievements
CREATE TABLE user_achievements (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    achievement_id INTEGER REFERENCES achievements(id),
    
    -- Progress
    progress JSONB DEFAULT '{}',
    is_completed BOOLEAN DEFAULT false,
    completed_at TIMESTAMP,
    
    -- Rewards claimed
    rewards_claimed BOOLEAN DEFAULT false,
    claimed_at TIMESTAMP,
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    UNIQUE(user_id, achievement_id)
);

-- Leaderboards
CREATE TABLE leaderboards (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    
    -- Leaderboard types
    leaderboard_type VARCHAR(50) NOT NULL, -- global, subject, weekly, monthly
    subject_id INTEGER REFERENCES subjects(id),
    
    -- Scores
    score INTEGER NOT NULL,
    rank INTEGER,
    
    -- Time period
    period_start DATE,
    period_end DATE,
    
    -- Metadata
    metadata JSONB,
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for practice domain
CREATE INDEX idx_practice_sessions_user ON practice_sessions(user_id);
CREATE INDEX idx_practice_sessions_quiz ON practice_sessions(quiz_id);
CREATE INDEX idx_currency_transactions_user ON currency_transactions(user_id);
CREATE INDEX idx_currency_transactions_type ON currency_transactions(transaction_type);
CREATE INDEX idx_user_skills_user ON user_skills(user_id);
CREATE INDEX idx_skill_usage_user ON skill_usage_history(user_id);
CREATE INDEX idx_user_achievements_user ON user_achievements(user_id);
CREATE INDEX idx_leaderboards_type ON leaderboards(leaderboard_type);
CREATE INDEX idx_leaderboards_subject ON leaderboards(subject_id);
EOF
```

## 🎯 Phase 3: Shared Components (Week 1)

### **Step 3.1: Event System**
```bash
# Create event definitions
mkdir -p shared/events/definitions

cat > shared/events/definitions/BaseEvent.js << 'EOF'
class BaseEvent {
  constructor(data, metadata = {}) {
    this.id = require('uuid').v4();
    this.type = this.constructor.name;
    this.data = data;
    this.metadata = {
      ...metadata,
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    };
  }

  toJSON() {
    return {
      id: this.id,
      type: this.type,
      data: this.data,
      metadata: this.metadata
    };
  }

  static fromJSON(json) {
    const event = new this(json.data, json.metadata);
    event.id = json.id;
    return event;
  }
}

module.exports = BaseEvent;
EOF

# Assessment events
cat > shared/events/definitions/AssessmentEvents.js << 'EOF'
const BaseEvent = require('./BaseEvent');

class AssessmentStarted extends BaseEvent {
  constructor(sessionId, userId, quizId, metadata = {}) {
    super({
      sessionId,
      userId,
      quizId,
      startedAt: new Date().toISOString()
    }, metadata);
  }
}

class AnswerSubmitted extends BaseEvent {
  constructor(sessionId, questionId, answer, isCorrect, metadata = {}) {
    super({
      sessionId,
      questionId,
      answer,
      isCorrect,
      submittedAt: new Date().toISOString()
    }, metadata);
  }
}

class AssessmentCompleted extends BaseEvent {
  constructor(sessionId, userId, results, metadata = {}) {
    super({
      sessionId,
      userId,
      results,
      completedAt: new Date().toISOString()
    }, metadata);
  }
}

class ViolationDetected extends BaseEvent {
  constructor(sessionId, violationType, severity, details, metadata = {}) {
    super({
      sessionId,
      violationType,
      severity,
      details,
      detectedAt: new Date().toISOString()
    }, metadata);
  }
}

module.exports = {
  AssessmentStarted,
  AnswerSubmitted,
  AssessmentCompleted,
  ViolationDetected
};
EOF

# Practice events
cat > shared/events/definitions/PracticeEvents.js << 'EOF'
const BaseEvent = require('./BaseEvent');

class PracticeStarted extends BaseEvent {
  constructor(sessionId, userId, quizId, metadata = {}) {
    super({
      sessionId,
      userId,
      quizId,
      startedAt: new Date().toISOString()
    }, metadata);
  }
}

class PointsEarned extends BaseEvent {
  constructor(userId, points, source, metadata = {}) {
    super({
      userId,
      points,
      source,
      earnedAt: new Date().toISOString()
    }, metadata);
  }
}

class CurrencyEarned extends BaseEvent {
  constructor(userId, currencyType, amount, source, metadata = {}) {
    super({
      userId,
      currencyType,
      amount,
      source,
      earnedAt: new Date().toISOString()
    }, metadata);
  }
}

class SkillUsed extends BaseEvent {
  constructor(userId, skillId, sessionId, effect, metadata = {}) {
    super({
      userId,
      skillId,
      sessionId,
      effect,
      usedAt: new Date().toISOString()
    }, metadata);
  }
}

class LevelUp extends BaseEvent {
  constructor(userId, newLevel, totalXP, rewards, metadata = {}) {
    super({
      userId,
      newLevel,
      totalXP,
      rewards,
      leveledUpAt: new Date().toISOString()
    }, metadata);
  }
}

class AchievementUnlocked extends BaseEvent {
  constructor(userId, achievementId, rewards, metadata = {}) {
    super({
      userId,
      achievementId,
      rewards,
      unlockedAt: new Date().toISOString()
    }, metadata);
  }
}

module.exports = {
  PracticeStarted,
  PointsEarned,
  CurrencyEarned,
  SkillUsed,
  LevelUp,
  AchievementUnlocked
};
EOF
```

### **Step 3.2: Database Connection**
```bash
# Create database utilities
cat > shared/database/connection.js << 'EOF'
const { Sequelize } = require('sequelize');

class DatabaseConnection {
  constructor() {
    this.sequelize = null;
  }

  async connect(config = {}) {
    const dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 5432,
      username: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || 'postgres',
      database: process.env.DB_NAME || 'ql_ctdt_v2',
      dialect: 'postgres',
      logging: process.env.NODE_ENV === 'development' ? console.log : false,
      pool: {
        max: 5,
        min: 0,
        acquire: 30000,
        idle: 10000
      },
      define: {
        timestamps: true,
        underscored: true,
        freezeTableName: true
      },
      ...config
    };

    this.sequelize = new Sequelize(dbConfig);

    try {
      await this.sequelize.authenticate();
      console.log('✅ Database connection established successfully.');
      return this.sequelize;
    } catch (error) {
      console.error('❌ Unable to connect to the database:', error);
      throw error;
    }
  }

  async disconnect() {
    if (this.sequelize) {
      await this.sequelize.close();
      console.log('🔌 Database connection closed.');
    }
  }

  getSequelize() {
    return this.sequelize;
  }
}

module.exports = new DatabaseConnection();
EOF

# Create base model
cat > shared/database/BaseModel.js << 'EOF'
const { Model, DataTypes } = require('sequelize');

class BaseModel extends Model {
  static init(attributes, options) {
    const baseAttributes = {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      uuid: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        unique: true
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW
      },
      updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW
      }
    };

    const mergedAttributes = {
      ...baseAttributes,
      ...attributes
    };

    const baseOptions = {
      timestamps: true,
      underscored: true,
      freezeTableName: true,
      ...options
    };

    return super.init(mergedAttributes, baseOptions);
  }

  // Common methods
  toJSON() {
    const values = { ...this.get() };
    delete values.id; // Hide internal ID, use UUID instead
    return values;
  }

  static async findByUuid(uuid) {
    return this.findOne({ where: { uuid } });
  }

  static async findByUuids(uuids) {
    return this.findAll({ where: { uuid: uuids } });
  }
}

module.exports = BaseModel;
EOF
```

### **Step 3.3: Authentication & Authorization**
```bash
# Create auth utilities
cat > shared/auth/JWTService.js << 'EOF'
const jwt = require('jsonwebtoken');

class JWTService {
  constructor() {
    this.secret = process.env.JWT_SECRET || 'your-secret-key';
    this.expiresIn = process.env.JWT_EXPIRES_IN || '24h';
  }

  generateToken(payload) {
    return jwt.sign(payload, this.secret, { expiresIn: this.expiresIn });
  }

  verifyToken(token) {
    try {
      return jwt.verify(token, this.secret);
    } catch (error) {
      throw new Error('Invalid token');
    }
  }

  generateRefreshToken(payload) {
    return jwt.sign(payload, this.secret, { expiresIn: '7d' });
  }

  decodeToken(token) {
    return jwt.decode(token);
  }
}

module.exports = new JWTService();
EOF

# Create auth middleware
cat > shared/middleware/auth.js << 'EOF'
const JWTService = require('../auth/JWTService');

const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({
      error: 'Access token required',
      code: 'TOKEN_REQUIRED'
    });
  }

  try {
    const decoded = JWTService.verifyToken(token);
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(403).json({
      error: 'Invalid or expired token',
      code: 'TOKEN_INVALID'
    });
  }
};

const authorizeRoles = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        error: 'Insufficient permissions',
        code: 'INSUFFICIENT_PERMISSIONS',
        required: roles,
        current: req.user.role
      });
    }

    next();
  };
};

const optionalAuth = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (token) {
    try {
      const decoded = JWTService.verifyToken(token);
      req.user = decoded;
    } catch (error) {
      // Token invalid but continue without auth
      req.user = null;
    }
  }

  next();
};

module.exports = {
  authenticateToken,
  authorizeRoles,
  optionalAuth
};
EOF
```

## 🚪 Phase 4: API Gateway (Week 2)

### **Step 4.1: Gateway Structure**
```bash
# Create API Gateway
mkdir -p gateway/api-gateway/src/{routes,middleware,services,config,utils}

cat > gateway/api-gateway/package.json << 'EOF'
{
  "name": "@ql-ctdt-v2/api-gateway",
  "version": "1.0.0",
  "description": "API Gateway for QL_CTDT_V2",
  "main": "src/app.js",
  "scripts": {
    "start": "node src/app.js",
    "dev": "nodemon src/app.js",
    "test": "jest",
    "lint": "eslint src/"
  },
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "helmet": "^6.0.1",
    "express-rate-limit": "^6.7.0",
    "http-proxy-middleware": "^2.0.6",
    "redis": "^4.6.5",
    "winston": "^3.8.2",
    "express-validator": "^6.15.0",
    "swagger-ui-express": "^4.6.2",
    "yamljs": "^0.3.0"
  },
  "devDependencies": {
    "nodemon": "^2.0.20",
    "jest": "^29.5.0",
    "eslint": "^8.38.0"
  }
}
EOF

# Main gateway app
cat > gateway/api-gateway/src/app.js << 'EOF'
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const { createProxyMiddleware } = require('http-proxy-middleware');

const logger = require('./utils/logger');
const errorHandler = require('./middleware/errorHandler');
const requestLogger = require('./middleware/requestLogger');
const serviceDiscovery = require('./services/ServiceDiscovery');
const healthCheck = require('./routes/health');

const app = express();
const PORT = process.env.API_GATEWAY_PORT || 8080;

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // limit each IP to 1000 requests per windowMs
  message: 'Too many requests from this IP'
});
app.use(limiter);

// Body parsing
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Request logging
app.use(requestLogger);

// Health check
app.use('/health', healthCheck);

// Service routes with proxy
const services = {
  // Assessment services
  '/api/assessment/quiz': {
    target: `http://localhost:${process.env.ASSESSMENT_QUIZ_ENGINE_PORT || 8081}`,
    pathRewrite: { '^/api/assessment/quiz': '' }
  },
  '/api/assessment/proctoring': {
    target: `http://localhost:${process.env.ASSESSMENT_PROCTORING_PORT || 8082}`,
    pathRewrite: { '^/api/assessment/proctoring': '' }
  },
  '/api/assessment/grading': {
    target: `http://localhost:${process.env.ASSESSMENT_GRADING_PORT || 8083}`,
    pathRewrite: { '^/api/assessment/grading': '' }
  },

  // Practice services
  '/api/practice/quiz': {
    target: `http://localhost:${process.env.PRACTICE_QUIZ_ENGINE_PORT || 8084}`,
    pathRewrite: { '^/api/practice/quiz': '' }
  },
  '/api/practice/gamification': {
    target: `http://localhost:${process.env.PRACTICE_GAMIFICATION_PORT || 8085}`,
    pathRewrite: { '^/api/practice/gamification': '' }
  },
  '/api/practice/currency': {
    target: `http://localhost:${process.env.PRACTICE_CURRENCY_PORT || 8086}`,
    pathRewrite: { '^/api/practice/currency': '' }
  },
  '/api/practice/skills': {
    target: `http://localhost:${process.env.PRACTICE_SKILLS_PORT || 8087}`,
    pathRewrite: { '^/api/practice/skills': '' }
  },
  '/api/practice/social': {
    target: `http://localhost:${process.env.PRACTICE_SOCIAL_PORT || 8088}`,
    pathRewrite: { '^/api/practice/social': '' }
  },

  // Content services
  '/api/content/quiz-builder': {
    target: `http://localhost:${process.env.CONTENT_QUIZ_BUILDER_PORT || 8089}`,
    pathRewrite: { '^/api/content/quiz-builder': '' }
  },
  '/api/content/questions': {
    target: `http://localhost:${process.env.CONTENT_QUESTION_BANK_PORT || 8090}`,
    pathRewrite: { '^/api/content/questions': '' }
  },
  '/api/content/curriculum': {
    target: `http://localhost:${process.env.CONTENT_CURRICULUM_PORT || 8091}`,
    pathRewrite: { '^/api/content/curriculum': '' }
  },
  '/api/content/media': {
    target: `http://localhost:${process.env.CONTENT_MEDIA_PORT || 8092}`,
    pathRewrite: { '^/api/content/media': '' }
  },

  // Analytics services
  '/api/analytics/data': {
    target: `http://localhost:${process.env.ANALYTICS_DATA_COLLECTION_PORT || 8093}`,
    pathRewrite: { '^/api/analytics/data': '' }
  },
  '/api/analytics/reports': {
    target: `http://localhost:${process.env.ANALYTICS_REPORTING_PORT || 8094}`,
    pathRewrite: { '^/api/analytics/reports': '' }
  },
  '/api/analytics/insights': {
    target: `http://localhost:${process.env.ANALYTICS_INSIGHTS_PORT || 8095}`,
    pathRewrite: { '^/api/analytics/insights': '' }
  },
  '/api/analytics/realtime': {
    target: `http://localhost:${process.env.ANALYTICS_REALTIME_PORT || 8096}`,
    pathRewrite: { '^/api/analytics/realtime': '' }
  }
};

// Setup proxies
Object.entries(services).forEach(([path, config]) => {
  app.use(path, createProxyMiddleware({
    ...config,
    changeOrigin: true,
    onError: (err, req, res) => {
      logger.error(`Proxy error for ${path}:`, err);
      res.status(503).json({
        error: 'Service temporarily unavailable',
        service: path
      });
    },
    onProxyReq: (proxyReq, req, res) => {
      // Add correlation ID
      proxyReq.setHeader('X-Correlation-ID', req.headers['x-correlation-id'] || require('uuid').v4());
    }
  }));
});

// Error handling
app.use(errorHandler);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    path: req.originalUrl
  });
});

// Start server
app.listen(PORT, () => {
  logger.info(`🚪 API Gateway running on port ${PORT}`);

  // Initialize service discovery
  serviceDiscovery.start();
});

module.exports = app;
EOF
```

### **Step 4.2: Gateway Services**
```bash
# Service Discovery
cat > gateway/api-gateway/src/services/ServiceDiscovery.js << 'EOF'
const axios = require('axios');
const logger = require('../utils/logger');

class ServiceDiscovery {
  constructor() {
    this.services = new Map();
    this.healthCheckInterval = 30000; // 30 seconds
    this.healthCheckTimer = null;
  }

  start() {
    logger.info('🔍 Starting service discovery...');
    this.discoverServices();
    this.startHealthChecks();
  }

  stop() {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }
  }

  async discoverServices() {
    const serviceConfigs = [
      // Assessment services
      { name: 'assessment-quiz-engine', port: process.env.ASSESSMENT_QUIZ_ENGINE_PORT || 8081 },
      { name: 'assessment-proctoring', port: process.env.ASSESSMENT_PROCTORING_PORT || 8082 },
      { name: 'assessment-grading', port: process.env.ASSESSMENT_GRADING_PORT || 8083 },

      // Practice services
      { name: 'practice-quiz-engine', port: process.env.PRACTICE_QUIZ_ENGINE_PORT || 8084 },
      { name: 'practice-gamification', port: process.env.PRACTICE_GAMIFICATION_PORT || 8085 },
      { name: 'practice-currency', port: process.env.PRACTICE_CURRENCY_PORT || 8086 },
      { name: 'practice-skills', port: process.env.PRACTICE_SKILLS_PORT || 8087 },
      { name: 'practice-social', port: process.env.PRACTICE_SOCIAL_PORT || 8088 },

      // Content services
      { name: 'content-quiz-builder', port: process.env.CONTENT_QUIZ_BUILDER_PORT || 8089 },
      { name: 'content-question-bank', port: process.env.CONTENT_QUESTION_BANK_PORT || 8090 },
      { name: 'content-curriculum', port: process.env.CONTENT_CURRICULUM_PORT || 8091 },
      { name: 'content-media', port: process.env.CONTENT_MEDIA_PORT || 8092 },

      // Analytics services
      { name: 'analytics-data-collection', port: process.env.ANALYTICS_DATA_COLLECTION_PORT || 8093 },
      { name: 'analytics-reporting', port: process.env.ANALYTICS_REPORTING_PORT || 8094 },
      { name: 'analytics-insights', port: process.env.ANALYTICS_INSIGHTS_PORT || 8095 },
      { name: 'analytics-realtime', port: process.env.ANALYTICS_REALTIME_PORT || 8096 }
    ];

    for (const config of serviceConfigs) {
      this.services.set(config.name, {
        ...config,
        url: `http://localhost:${config.port}`,
        healthy: false,
        lastCheck: null
      });
    }

    logger.info(`📋 Discovered ${this.services.size} services`);
  }

  startHealthChecks() {
    this.healthCheckTimer = setInterval(() => {
      this.checkAllServices();
    }, this.healthCheckInterval);

    // Initial health check
    this.checkAllServices();
  }

  async checkAllServices() {
    const promises = Array.from(this.services.entries()).map(([name, service]) =>
      this.checkServiceHealth(name, service)
    );

    await Promise.allSettled(promises);
  }

  async checkServiceHealth(name, service) {
    try {
      const response = await axios.get(`${service.url}/health`, {
        timeout: 5000
      });

      const wasUnhealthy = !service.healthy;
      service.healthy = response.status === 200;
      service.lastCheck = new Date();

      if (wasUnhealthy && service.healthy) {
        logger.info(`✅ Service ${name} is now healthy`);
      }
    } catch (error) {
      const wasHealthy = service.healthy;
      service.healthy = false;
      service.lastCheck = new Date();

      if (wasHealthy) {
        logger.warn(`❌ Service ${name} is now unhealthy: ${error.message}`);
      }
    }
  }

  getServiceStatus() {
    const status = {};
    this.services.forEach((service, name) => {
      status[name] = {
        healthy: service.healthy,
        url: service.url,
        lastCheck: service.lastCheck
      };
    });
    return status;
  }

  getHealthyServices() {
    const healthy = {};
    this.services.forEach((service, name) => {
      if (service.healthy) {
        healthy[name] = service;
      }
    });
    return healthy;
  }
}

module.exports = new ServiceDiscovery();
EOF

# Request Logger
cat > gateway/api-gateway/src/middleware/requestLogger.js << 'EOF'
const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

const requestLogger = (req, res, next) => {
  // Add correlation ID
  req.correlationId = req.headers['x-correlation-id'] || uuidv4();
  res.setHeader('X-Correlation-ID', req.correlationId);

  const start = Date.now();

  // Log request
  logger.info('📥 Incoming request', {
    correlationId: req.correlationId,
    method: req.method,
    url: req.originalUrl,
    userAgent: req.headers['user-agent'],
    ip: req.ip,
    userId: req.user?.id
  });

  // Override res.end to log response
  const originalEnd = res.end;
  res.end = function(chunk, encoding) {
    const duration = Date.now() - start;

    logger.info('📤 Outgoing response', {
      correlationId: req.correlationId,
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      userId: req.user?.id
    });

    originalEnd.call(this, chunk, encoding);
  };

  next();
};

module.exports = requestLogger;
EOF

# Error Handler
cat > gateway/api-gateway/src/middleware/errorHandler.js << 'EOF'
const logger = require('../utils/logger');

const errorHandler = (err, req, res, next) => {
  logger.error('🚨 Gateway error', {
    correlationId: req.correlationId,
    error: err.message,
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    userId: req.user?.id
  });

  // Default error response
  let statusCode = 500;
  let message = 'Internal server error';
  let code = 'INTERNAL_ERROR';

  // Handle specific error types
  if (err.name === 'ValidationError') {
    statusCode = 400;
    message = 'Validation error';
    code = 'VALIDATION_ERROR';
  } else if (err.name === 'UnauthorizedError') {
    statusCode = 401;
    message = 'Unauthorized';
    code = 'UNAUTHORIZED';
  } else if (err.name === 'ForbiddenError') {
    statusCode = 403;
    message = 'Forbidden';
    code = 'FORBIDDEN';
  } else if (err.name === 'NotFoundError') {
    statusCode = 404;
    message = 'Not found';
    code = 'NOT_FOUND';
  }

  res.status(statusCode).json({
    error: message,
    code,
    correlationId: req.correlationId,
    timestamp: new Date().toISOString(),
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
};

module.exports = errorHandler;
EOF

# Health Check Route
cat > gateway/api-gateway/src/routes/health.js << 'EOF'
const express = require('express');
const serviceDiscovery = require('../services/ServiceDiscovery');
const router = express.Router();

router.get('/', (req, res) => {
  const services = serviceDiscovery.getServiceStatus();
  const healthyCount = Object.values(services).filter(s => s.healthy).length;
  const totalCount = Object.keys(services).length;

  const isHealthy = healthyCount === totalCount;

  res.status(isHealthy ? 200 : 503).json({
    status: isHealthy ? 'healthy' : 'degraded',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    services: {
      total: totalCount,
      healthy: healthyCount,
      unhealthy: totalCount - healthyCount,
      details: services
    }
  });
});

router.get('/services', (req, res) => {
  const services = serviceDiscovery.getServiceStatus();
  res.json(services);
});

module.exports = router;
EOF

# Logger utility
cat > gateway/api-gateway/src/utils/logger.js << 'EOF'
const winston = require('winston');

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'api-gateway' },
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' })
  ]
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }));
}

module.exports = logger;
EOF

# Gateway Dockerfile
cat > gateway/api-gateway/Dockerfile << 'EOF'
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy source code
COPY src/ ./src/

# Create logs directory
RUN mkdir -p logs

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1

EXPOSE 8080

CMD ["node", "src/app.js"]
EOF
```

## 🌐 Phase 5: Web Portal (Week 2)

### **Step 5.1: Next.js Web Portal**
```bash
# Create Web Portal
mkdir -p gateway/web-portal

cat > gateway/web-portal/package.json << 'EOF'
{
  "name": "@ql-ctdt-v2/web-portal",
  "version": "1.0.0",
  "description": "Main Web Portal for QL_CTDT_V2",
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint"
  },
  "dependencies": {
    "next": "^13.4.0",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "axios": "^1.4.0",
    "socket.io-client": "^4.6.1",
    "react-query": "^3.39.3",
    "react-hook-form": "^7.43.9",
    "react-hot-toast": "^2.4.1",
    "tailwindcss": "^3.3.0",
    "autoprefixer": "^10.4.14",
    "postcss": "^8.4.23",
    "@headlessui/react": "^1.7.14",
    "@heroicons/react": "^2.0.17",
    "clsx": "^1.2.1",
    "js-cookie": "^3.0.5"
  },
  "devDependencies": {
    "eslint": "^8.40.0",
    "eslint-config-next": "^13.4.0",
    "@types/node": "^18.16.3",
    "@types/react": "^18.2.6",
    "@types/react-dom": "^18.2.4",
    "typescript": "^5.0.4"
  }
}
EOF

# Next.js config
cat > gateway/web-portal/next.config.js << 'EOF'
/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  env: {
    API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080',
    WS_URL: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8080'
  },
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080'}/api/:path*`
      }
    ];
  },
  async redirects() {
    return [
      {
        source: '/assessment/:path*',
        destination: `http://localhost:${process.env.ASSESSMENT_UI_PORT || 3001}/:path*`,
        permanent: false,
        basePath: false
      },
      {
        source: '/practice/:path*',
        destination: `http://localhost:${process.env.PRACTICE_UI_PORT || 3002}/:path*`,
        permanent: false,
        basePath: false
      }
    ];
  }
};

module.exports = nextConfig;
EOF

# Main layout
cat > gateway/web-portal/src/pages/_app.js << 'EOF'
import { QueryClient, QueryClientProvider } from 'react-query';
import { Toaster } from 'react-hot-toast';
import { AuthProvider } from '../contexts/AuthContext';
import { WebSocketProvider } from '../contexts/WebSocketContext';
import Layout from '../components/Layout/Layout';
import '../styles/globals.css';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function MyApp({ Component, pageProps }) {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <WebSocketProvider>
          <Layout>
            <Component {...pageProps} />
            <Toaster position="top-right" />
          </Layout>
        </WebSocketProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default MyApp;
EOF

# Home page
cat > gateway/web-portal/src/pages/index.js << 'EOF'
import { useState } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useRouter } from 'next/router';
import PINEntry from '../components/Quiz/PINEntry';
import DashboardStats from '../components/Dashboard/DashboardStats';
import RecentActivity from '../components/Dashboard/RecentActivity';

export default function Home() {
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();
  const [showPINEntry, setShowPINEntry] = useState(false);

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="container mx-auto px-4 py-16">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-8">
              QL_CTDT V2 - Hệ thống Quản lý Chương trình Đào tạo
            </h1>
            <p className="text-xl text-gray-600 mb-12">
              Nền tảng học tập và đánh giá thông minh với công nghệ AI
            </p>

            <div className="max-w-md mx-auto space-y-4">
              <button
                onClick={() => router.push('/auth/login')}
                className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Đăng nhập
              </button>

              <button
                onClick={() => setShowPINEntry(true)}
                className="w-full bg-green-600 text-white py-3 px-6 rounded-lg hover:bg-green-700 transition-colors"
              >
                Tham gia bài thi/luyện tập
              </button>
            </div>
          </div>
        </div>

        {showPINEntry && (
          <PINEntry onClose={() => setShowPINEntry(false)} />
        )}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white shadow rounded-lg p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Chào mừng, {user?.first_name} {user?.last_name}!
        </h1>
        <p className="text-gray-600">
          {user?.role === 'teacher' ? 'Quản lý bài thi và theo dõi tiến độ học sinh' :
           user?.role === 'student' ? 'Tham gia bài thi và luyện tập' :
           'Quản lý hệ thống'}
        </p>
      </div>

      <DashboardStats />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <RecentActivity />

        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            Truy cập nhanh
          </h2>

          <div className="space-y-3">
            {user?.role === 'teacher' && (
              <>
                <button
                  onClick={() => router.push('/teacher/create-quiz')}
                  className="w-full text-left p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors"
                >
                  <div className="font-medium text-blue-900">Tạo bài thi mới</div>
                  <div className="text-sm text-blue-600">Tạo bài thi đánh giá hoặc luyện tập</div>
                </button>

                <button
                  onClick={() => router.push('/teacher/monitor')}
                  className="w-full text-left p-3 bg-green-50 hover:bg-green-100 rounded-lg transition-colors"
                >
                  <div className="font-medium text-green-900">Theo dõi bài thi</div>
                  <div className="text-sm text-green-600">Giám sát học sinh làm bài</div>
                </button>
              </>
            )}

            {user?.role === 'student' && (
              <>
                <button
                  onClick={() => setShowPINEntry(true)}
                  className="w-full text-left p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors"
                >
                  <div className="font-medium text-purple-900">Nhập mã PIN</div>
                  <div className="text-sm text-purple-600">Tham gia bài thi hoặc luyện tập</div>
                </button>

                <button
                  onClick={() => router.push('/student/practice')}
                  className="w-full text-left p-3 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors"
                >
                  <div className="font-medium text-orange-900">Luyện tập tự do</div>
                  <div className="text-sm text-orange-600">Chế độ luyện tập với gamification</div>
                </button>
              </>
            )}
          </div>
        </div>
      </div>

      {showPINEntry && (
        <PINEntry onClose={() => setShowPINEntry(false)} />
      )}
    </div>
  );
}
EOF
```

## 📱 Phase 6: Assessment Frontend (Week 3)

### **Step 6.1: Assessment UI Structure**
```bash
# Create Assessment UI
mkdir -p domains/assessment/frontend/assessment-ui/src/{components,pages,hooks,services,utils}

cat > domains/assessment/frontend/assessment-ui/package.json << 'EOF'
{
  "name": "@ql-ctdt-v2/assessment-ui",
  "version": "1.0.0",
  "description": "Assessment UI for QL_CTDT_V2",
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-scripts": "^5.0.1",
    "react-router-dom": "^6.11.0",
    "axios": "^1.4.0",
    "socket.io-client": "^4.6.1",
    "react-query": "^3.39.3",
    "react-hook-form": "^7.43.9",
    "react-hot-toast": "^2.4.1",
    "tailwindcss": "^3.3.0",
    "autoprefixer": "^10.4.14",
    "postcss": "^8.4.23",
    "@headlessui/react": "^1.7.14",
    "@heroicons/react": "^2.0.17",
    "clsx": "^1.2.1"
  },
  "browserslist": {
    "production": [
      ">0.2%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  },
  "proxy": "http://localhost:8080"
}
EOF

# Main Assessment App
cat > domains/assessment/frontend/assessment-ui/src/App.js << 'EOF'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from 'react-query';
import { Toaster } from 'react-hot-toast';
import { AssessmentProvider } from './contexts/AssessmentContext';
import { ProctoringProvider } from './contexts/ProctoringContext';

import TakeQuiz from './pages/TakeQuiz';
import QuizInstructions from './pages/QuizInstructions';
import QuizResults from './pages/QuizResults';
import AssessmentHistory from './pages/AssessmentHistory';
import NotFound from './pages/NotFound';

import './styles/globals.css';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AssessmentProvider>
        <ProctoringProvider>
          <Router>
            <div className="min-h-screen bg-gray-50">
              <Routes>
                <Route path="/quiz/:sessionId/instructions" element={<QuizInstructions />} />
                <Route path="/quiz/:sessionId" element={<TakeQuiz />} />
                <Route path="/quiz/:sessionId/results" element={<QuizResults />} />
                <Route path="/history" element={<AssessmentHistory />} />
                <Route path="/" element={<QuizInstructions />} />
                <Route path="*" element={<NotFound />} />
              </Routes>
              <Toaster position="top-right" />
            </div>
          </Router>
        </ProctoringProvider>
      </AssessmentProvider>
    </QueryClientProvider>
  );
}

export default App;
EOF

# Quiz Taking Page
cat > domains/assessment/frontend/assessment-ui/src/pages/TakeQuiz.js << 'EOF'
import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAssessment } from '../hooks/useAssessment';
import { useProctoring } from '../hooks/useProctoring';
import { useTimer } from '../hooks/useTimer';

import QuizHeader from '../components/QuizInterface/QuizHeader';
import QuestionDisplay from '../components/QuizInterface/QuestionDisplay';
import QuestionNavigation from '../components/QuizInterface/QuestionNavigation';
import Timer from '../components/Timer/Timer';
import ProctoringPanel from '../components/Proctoring/ProctoringPanel';
import SubmissionModal from '../components/Submission/SubmissionModal';

export default function TakeQuiz() {
  const { sessionId } = useParams();
  const navigate = useNavigate();

  const {
    quiz,
    currentQuestion,
    currentQuestionIndex,
    answers,
    isLoading,
    submitAnswer,
    submitQuiz,
    goToQuestion,
    nextQuestion,
    previousQuestion
  } = useAssessment(sessionId);

  const {
    isMonitoring,
    violations,
    startMonitoring,
    stopMonitoring
  } = useProctoring(sessionId);

  const {
    timeRemaining,
    isTimeUp,
    startTimer,
    pauseTimer
  } = useTimer(quiz?.time_limit * 60); // Convert minutes to seconds

  const [showSubmissionModal, setShowSubmissionModal] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (quiz && quiz.enable_proctoring) {
      startMonitoring();
    }

    if (quiz && quiz.time_limit) {
      startTimer();
    }

    return () => {
      stopMonitoring();
      pauseTimer();
    };
  }, [quiz]);

  useEffect(() => {
    if (isTimeUp) {
      handleSubmitQuiz();
    }
  }, [isTimeUp]);

  const handleAnswerSubmit = async (answer) => {
    try {
      await submitAnswer(currentQuestion.id, answer);
    } catch (error) {
      console.error('Error submitting answer:', error);
    }
  };

  const handleSubmitQuiz = async () => {
    setIsSubmitting(true);
    try {
      const result = await submitQuiz();
      navigate(`/quiz/${sessionId}/results`, {
        state: { result }
      });
    } catch (error) {
      console.error('Error submitting quiz:', error);
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!quiz || !currentQuestion) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Không tìm thấy bài thi
          </h1>
          <button
            onClick={() => navigate('/')}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
          >
            Quay về trang chủ
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <QuizHeader
        quiz={quiz}
        currentQuestionIndex={currentQuestionIndex}
        totalQuestions={quiz.questions?.length || 0}
      />

      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Main Quiz Area */}
          <div className="lg:col-span-3 space-y-6">
            {/* Timer */}
            {quiz.time_limit && (
              <div className="bg-white rounded-lg shadow p-4">
                <Timer
                  timeRemaining={timeRemaining}
                  isTimeUp={isTimeUp}
                  totalTime={quiz.time_limit * 60}
                />
              </div>
            )}

            {/* Question */}
            <div className="bg-white rounded-lg shadow">
              <QuestionDisplay
                question={currentQuestion}
                answer={answers[currentQuestion.id]}
                onAnswerChange={handleAnswerSubmit}
                isReadOnly={false}
              />
            </div>

            {/* Navigation */}
            <div className="bg-white rounded-lg shadow p-4">
              <div className="flex justify-between items-center">
                <button
                  onClick={previousQuestion}
                  disabled={currentQuestionIndex === 0}
                  className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Câu trước
                </button>

                <span className="text-gray-600">
                  Câu {currentQuestionIndex + 1} / {quiz.questions?.length || 0}
                </span>

                {currentQuestionIndex === (quiz.questions?.length || 0) - 1 ? (
                  <button
                    onClick={() => setShowSubmissionModal(true)}
                    className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700"
                  >
                    Nộp bài
                  </button>
                ) : (
                  <button
                    onClick={nextQuestion}
                    className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
                  >
                    Câu tiếp
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Question Navigation */}
            <QuestionNavigation
              questions={quiz.questions || []}
              answers={answers}
              currentQuestionIndex={currentQuestionIndex}
              onQuestionSelect={goToQuestion}
            />

            {/* Proctoring Panel */}
            {quiz.enable_proctoring && (
              <ProctoringPanel
                isMonitoring={isMonitoring}
                violations={violations}
              />
            )}
          </div>
        </div>
      </div>

      {/* Submission Modal */}
      {showSubmissionModal && (
        <SubmissionModal
          quiz={quiz}
          answers={answers}
          onConfirm={handleSubmitQuiz}
          onCancel={() => setShowSubmissionModal(false)}
          isSubmitting={isSubmitting}
        />
      )}
    </div>
  );
}
EOF
```

## 🎮 Phase 7: Practice Frontend (Week 3)

### **Step 7.1: Practice UI with Gaming Features**
```bash
# Create Practice UI
mkdir -p domains/practice/frontend/practice-ui/src/{components,pages,hooks,services,game-engine}

cat > domains/practice/frontend/practice-ui/package.json << 'EOF'
{
  "name": "@ql-ctdt-v2/practice-ui",
  "version": "1.0.0",
  "description": "Practice UI with Gamification for QL_CTDT_V2",
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-scripts": "^5.0.1",
    "react-router-dom": "^6.11.0",
    "axios": "^1.4.0",
    "socket.io-client": "^4.6.1",
    "react-query": "^3.39.3",
    "framer-motion": "^10.12.4",
    "react-spring": "^9.7.1",
    "react-confetti": "^6.1.0",
    "react-hot-toast": "^2.4.1",
    "tailwindcss": "^3.3.0",
    "@headlessui/react": "^1.7.14",
    "@heroicons/react": "^2.0.17",
    "clsx": "^1.2.1",
    "canvas-confetti": "^1.6.0"
  },
  "proxy": "http://localhost:8080"
}
EOF

# Main Practice App
cat > domains/practice/frontend/practice-ui/src/App.js << 'EOF'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from 'react-query';
import { Toaster } from 'react-hot-toast';
import { GameProvider } from './contexts/GameContext';
import { SkillProvider } from './contexts/SkillContext';
import { CurrencyProvider } from './contexts/CurrencyContext';

import PracticeQuiz from './pages/PracticeQuiz';
import SkillTree from './pages/SkillTree';
import Shop from './pages/Shop';
import Leaderboard from './pages/Leaderboard';
import Profile from './pages/Profile';
import Achievements from './pages/Achievements';
import GameLayout from './components/Layout/GameLayout';

import './styles/globals.css';
import './styles/animations.css';

const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <GameProvider>
        <SkillProvider>
          <CurrencyProvider>
            <Router>
              <GameLayout>
                <Routes>
                  <Route path="/quiz/:sessionId" element={<PracticeQuiz />} />
                  <Route path="/skills" element={<SkillTree />} />
                  <Route path="/shop" element={<Shop />} />
                  <Route path="/leaderboard" element={<Leaderboard />} />
                  <Route path="/profile" element={<Profile />} />
                  <Route path="/achievements" element={<Achievements />} />
                  <Route path="/" element={<PracticeQuiz />} />
                </Routes>
              </GameLayout>
              <Toaster position="top-right" />
            </Router>
          </CurrencyProvider>
        </SkillProvider>
      </GameProvider>
    </QueryClientProvider>
  );
}

export default App;
EOF

# Game Layout with HUD
cat > domains/practice/frontend/practice-ui/src/components/Layout/GameLayout.js << 'EOF'
import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useGame } from '../../hooks/useGame';
import { useCurrency } from '../../hooks/useCurrency';
import { useSkills } from '../../hooks/useSkills';

import GameHUD from './GameHUD';
import SkillPanel from '../Skills/SkillPanel';
import NotificationSystem from '../Notifications/NotificationSystem';
import EffectOverlay from '../Effects/EffectOverlay';

export default function GameLayout({ children }) {
  const { gameState, activeEffects } = useGame();
  const { balance } = useCurrency();
  const { userSkills, activeSkills } = useSkills();
  const [showSkillPanel, setShowSkillPanel] = useState(false);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-blue-400 to-purple-400 animate-pulse"></div>
        <div className="absolute top-0 left-0 w-full h-full">
          {[...Array(50)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-white rounded-full animate-twinkle"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
                animationDuration: `${2 + Math.random() * 2}s`
              }}
            />
          ))}
        </div>
      </div>

      {/* Game HUD */}
      <GameHUD
        level={gameState.level}
        xp={gameState.xp}
        xpToNext={gameState.xpToNext}
        coins={balance.coins}
        gems={balance.gems}
        streak={gameState.streak}
        onSkillPanelToggle={() => setShowSkillPanel(!showSkillPanel)}
      />

      {/* Main Content */}
      <main className="relative z-10 pt-20">
        {children}
      </main>

      {/* Skill Panel */}
      <AnimatePresence>
        {showSkillPanel && (
          <SkillPanel
            skills={userSkills}
            activeSkills={activeSkills}
            onClose={() => setShowSkillPanel(false)}
          />
        )}
      </AnimatePresence>

      {/* Effect Overlay */}
      <EffectOverlay effects={activeEffects} />

      {/* Notification System */}
      <NotificationSystem />
    </div>
  );
}
EOF

# Game HUD Component
cat > domains/practice/frontend/practice-ui/src/components/Layout/GameHUD.js << 'EOF'
import { motion } from 'framer-motion';
import {
  StarIcon,
  BoltIcon,
  CurrencyDollarIcon,
  GemIcon,
  FireIcon
} from '@heroicons/react/24/solid';

export default function GameHUD({
  level,
  xp,
  xpToNext,
  coins,
  gems,
  streak,
  onSkillPanelToggle
}) {
  const xpPercentage = (xp / xpToNext) * 100;

  return (
    <div className="fixed top-0 left-0 right-0 z-50 bg-black bg-opacity-50 backdrop-blur-sm border-b border-white border-opacity-20">
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Left Side - Level & XP */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <StarIcon className="w-6 h-6 text-yellow-400" />
              <span className="text-white font-bold text-lg">Lv.{level}</span>
            </div>

            <div className="w-48 bg-gray-700 rounded-full h-3 relative overflow-hidden">
              <motion.div
                className="h-full bg-gradient-to-r from-blue-400 to-purple-400 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${xpPercentage}%` }}
                transition={{ duration: 0.5 }}
              />
              <div className="absolute inset-0 flex items-center justify-center text-xs text-white font-medium">
                {xp} / {xpToNext} XP
              </div>
            </div>
          </div>

          {/* Center - Streak */}
          {streak > 0 && (
            <motion.div
              className="flex items-center space-x-2 bg-orange-500 bg-opacity-20 px-3 py-1 rounded-full border border-orange-400"
              animate={{ scale: [1, 1.05, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <FireIcon className="w-5 h-5 text-orange-400" />
              <span className="text-orange-300 font-bold">{streak} Streak!</span>
            </motion.div>
          )}

          {/* Right Side - Currency & Skills */}
          <div className="flex items-center space-x-4">
            {/* Currency */}
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-1 bg-yellow-500 bg-opacity-20 px-2 py-1 rounded-full">
                <CurrencyDollarIcon className="w-4 h-4 text-yellow-400" />
                <span className="text-yellow-300 font-bold">{coins.toLocaleString()}</span>
              </div>

              <div className="flex items-center space-x-1 bg-purple-500 bg-opacity-20 px-2 py-1 rounded-full">
                <GemIcon className="w-4 h-4 text-purple-400" />
                <span className="text-purple-300 font-bold">{gems.toLocaleString()}</span>
              </div>
            </div>

            {/* Skills Button */}
            <button
              onClick={onSkillPanelToggle}
              className="flex items-center space-x-2 bg-blue-500 bg-opacity-20 hover:bg-opacity-30 px-3 py-2 rounded-lg border border-blue-400 transition-all"
            >
              <BoltIcon className="w-5 h-5 text-blue-400" />
              <span className="text-blue-300 font-medium">Skills</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
EOF

# Practice Quiz Page
cat > domains/practice/frontend/practice-ui/src/pages/PracticeQuiz.js << 'EOF'
import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useGame } from '../hooks/useGame';
import { useSkills } from '../hooks/useSkills';
import { usePractice } from '../hooks/usePractice';

import GameQuestionDisplay from '../components/GameInterface/GameQuestionDisplay';
import SkillButtons from '../components/Skills/SkillButtons';
import ProgressIndicator from '../components/Progress/ProgressIndicator';
import RewardAnimation from '../components/Animations/RewardAnimation';
import LevelUpModal from '../components/Modals/LevelUpModal';
import AchievementUnlock from '../components/Achievements/AchievementUnlock';

export default function PracticeQuiz() {
  const { sessionId } = useParams();
  const {
    quiz,
    currentQuestion,
    currentQuestionIndex,
    totalQuestions,
    submitAnswer,
    nextQuestion,
    isLoading
  } = usePractice(sessionId);

  const {
    gameState,
    addXP,
    addCurrency,
    checkLevelUp,
    checkAchievements
  } = useGame();

  const {
    userSkills,
    useSkill,
    getAvailableSkills
  } = useSkills();

  const [showReward, setShowReward] = useState(null);
  const [showLevelUp, setShowLevelUp] = useState(null);
  const [showAchievement, setShowAchievement] = useState(null);
  const [selectedAnswer, setSelectedAnswer] = useState(null);
  const [isAnswering, setIsAnswering] = useState(false);

  const availableSkills = getAvailableSkills();

  const handleAnswerSubmit = async (answer) => {
    if (isAnswering) return;

    setIsAnswering(true);
    setSelectedAnswer(answer);

    try {
      const result = await submitAnswer(currentQuestion.id, answer);

      if (result.isCorrect) {
        // Calculate rewards
        const baseXP = currentQuestion.base_xp || 10;
        const baseCoins = currentQuestion.base_coins || 5;
        const multiplier = currentQuestion.difficulty_multiplier || 1;

        const xpEarned = Math.floor(baseXP * multiplier);
        const coinsEarned = Math.floor(baseCoins * multiplier);

        // Apply skill effects
        const skillMultiplier = getActiveSkillMultiplier();
        const finalXP = Math.floor(xpEarned * skillMultiplier.xp);
        const finalCoins = Math.floor(coinsEarned * skillMultiplier.coins);

        // Add rewards
        addXP(finalXP);
        addCurrency('coins', finalCoins);

        // Show reward animation
        setShowReward({
          xp: finalXP,
          coins: finalCoins,
          isCorrect: true
        });

        // Check for level up
        const levelUpResult = checkLevelUp();
        if (levelUpResult.leveledUp) {
          setTimeout(() => {
            setShowLevelUp(levelUpResult);
          }, 1500);
        }

        // Check for achievements
        const achievements = checkAchievements();
        if (achievements.length > 0) {
          setTimeout(() => {
            setShowAchievement(achievements[0]);
          }, levelUpResult.leveledUp ? 3000 : 1500);
        }
      } else {
        setShowReward({
          isCorrect: false,
          message: 'Sai rồi! Hãy thử lại.'
        });
      }

      // Auto advance after delay
      setTimeout(() => {
        nextQuestion();
        setSelectedAnswer(null);
        setIsAnswering(false);
        setShowReward(null);
      }, 2000);

    } catch (error) {
      console.error('Error submitting answer:', error);
      setIsAnswering(false);
    }
  };

  const handleSkillUse = async (skillId) => {
    try {
      const result = await useSkill(skillId, {
        questionId: currentQuestion.id,
        sessionId
      });

      if (result.success) {
        // Apply skill effect to UI
        applySkillEffect(result.effect);
      }
    } catch (error) {
      console.error('Error using skill:', error);
    }
  };

  const getActiveSkillMultiplier = () => {
    // Calculate multipliers from active skills
    return {
      xp: 1.0, // Base multiplier
      coins: 1.0
    };
  };

  const applySkillEffect = (effect) => {
    // Apply visual and functional effects based on skill type
    switch (effect.type) {
      case 'time_freeze':
        // Freeze timer
        break;
      case 'double_points':
        // Show double points indicator
        break;
      case 'hint_reveal':
        // Show hint for current question
        break;
      default:
        break;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <motion.div
          className="w-16 h-16 border-4 border-blue-400 border-t-transparent rounded-full"
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Progress */}
        <ProgressIndicator
          current={currentQuestionIndex + 1}
          total={totalQuestions}
          percentage={(currentQuestionIndex / totalQuestions) * 100}
        />

        {/* Skills Panel */}
        <SkillButtons
          skills={availableSkills}
          onSkillUse={handleSkillUse}
          disabled={isAnswering}
        />

        {/* Question */}
        <motion.div
          key={currentQuestion?.id}
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -50 }}
          transition={{ duration: 0.3 }}
        >
          <GameQuestionDisplay
            question={currentQuestion}
            onAnswerSubmit={handleAnswerSubmit}
            selectedAnswer={selectedAnswer}
            isAnswering={isAnswering}
            showHint={false} // Will be controlled by skills
          />
        </motion.div>

        {/* Reward Animation */}
        <AnimatePresence>
          {showReward && (
            <RewardAnimation
              reward={showReward}
              onComplete={() => setShowReward(null)}
            />
          )}
        </AnimatePresence>

        {/* Level Up Modal */}
        <AnimatePresence>
          {showLevelUp && (
            <LevelUpModal
              levelUpData={showLevelUp}
              onClose={() => setShowLevelUp(null)}
            />
          )}
        </AnimatePresence>

        {/* Achievement Unlock */}
        <AnimatePresence>
          {showAchievement && (
            <AchievementUnlock
              achievement={showAchievement}
              onClose={() => setShowAchievement(null)}
            />
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
EOF
```

## 🏗️ Phase 8: Backend Services (Week 4-6)

### **Step 8.1: Assessment Quiz Engine Service**
```bash
# Create Assessment Quiz Engine
mkdir -p domains/assessment/services/quiz-engine/src/{controllers,services,models,routes,middleware}

cat > domains/assessment/services/quiz-engine/package.json << 'EOF'
{
  "name": "@ql-ctdt-v2/assessment-quiz-engine",
  "version": "1.0.0",
  "description": "Assessment Quiz Engine Service",
  "main": "src/app.js",
  "scripts": {
    "start": "node src/app.js",
    "dev": "nodemon src/app.js",
    "test": "jest"
  },
  "dependencies": {
    "express": "^4.18.2",
    "sequelize": "^6.31.0",
    "pg": "^8.10.0",
    "redis": "^4.6.5",
    "socket.io": "^4.6.1",
    "cors": "^2.8.5",
    "helmet": "^6.0.1",
    "express-rate-limit": "^6.7.0",
    "express-validator": "^6.15.0",
    "winston": "^3.8.2",
    "uuid": "^9.0.0"
  },
  "devDependencies": {
    "nodemon": "^2.0.20",
    "jest": "^29.5.0"
  }
}
EOF

# Main app
cat > domains/assessment/services/quiz-engine/src/app.js << 'EOF'
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const { createServer } = require('http');
const { Server } = require('socket.io');

const database = require('../../../../shared/database/connection');
const logger = require('./utils/logger');
const errorHandler = require('./middleware/errorHandler');

// Routes
const quizRoutes = require('./routes/quizRoutes');
const sessionRoutes = require('./routes/sessionRoutes');
const answerRoutes = require('./routes/answerRoutes');
const healthRoutes = require('./routes/healthRoutes');

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3001'],
    methods: ['GET', 'POST']
  }
});

const PORT = process.env.ASSESSMENT_QUIZ_ENGINE_PORT || 8081;

// Security middleware
app.use(helmet());
app.use(cors());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 1000
});
app.use(limiter);

// Body parsing
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Socket.IO for real-time features
io.on('connection', (socket) => {
  logger.info(`Client connected: ${socket.id}`);

  socket.on('join-session', (sessionId) => {
    socket.join(`session-${sessionId}`);
    logger.info(`Client ${socket.id} joined session ${sessionId}`);
  });

  socket.on('disconnect', () => {
    logger.info(`Client disconnected: ${socket.id}`);
  });
});

// Make io available to routes
app.set('io', io);

// Routes
app.use('/health', healthRoutes);
app.use('/quiz', quizRoutes);
app.use('/session', sessionRoutes);
app.use('/answer', answerRoutes);

// Error handling
app.use(errorHandler);

// Start server
async function startServer() {
  try {
    // Connect to database
    await database.connect();

    server.listen(PORT, () => {
      logger.info(`🎯 Assessment Quiz Engine running on port ${PORT}`);
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

startServer();

module.exports = app;
EOF

# Quiz Controller
cat > domains/assessment/services/quiz-engine/src/controllers/QuizController.js << 'EOF'
const QuizService = require('../services/QuizService');
const SessionService = require('../services/SessionService');
const logger = require('../utils/logger');

class QuizController {
  async getQuiz(req, res) {
    try {
      const { quizId } = req.params;
      const quiz = await QuizService.getQuizById(quizId);

      if (!quiz) {
        return res.status(404).json({
          error: 'Quiz not found',
          code: 'QUIZ_NOT_FOUND'
        });
      }

      res.json({
        success: true,
        data: quiz
      });
    } catch (error) {
      logger.error('Error getting quiz:', error);
      res.status(500).json({
        error: 'Failed to get quiz',
        code: 'GET_QUIZ_ERROR'
      });
    }
  }

  async getQuizBySession(req, res) {
    try {
      const { sessionId } = req.params;
      const session = await SessionService.getSessionById(sessionId);

      if (!session) {
        return res.status(404).json({
          error: 'Session not found',
          code: 'SESSION_NOT_FOUND'
        });
      }

      const quiz = await QuizService.getQuizForSession(session.quiz_id, session.user_id);

      res.json({
        success: true,
        data: {
          quiz,
          session: {
            id: session.id,
            uuid: session.uuid,
            started_at: session.started_at,
            time_spent: session.time_spent,
            status: session.status
          }
        }
      });
    } catch (error) {
      logger.error('Error getting quiz by session:', error);
      res.status(500).json({
        error: 'Failed to get quiz',
        code: 'GET_QUIZ_SESSION_ERROR'
      });
    }
  }

  async validateQuizAccess(req, res) {
    try {
      const { quizId } = req.params;
      const { userId } = req.body;

      const validation = await QuizService.validateAccess(quizId, userId);

      res.json({
        success: true,
        data: validation
      });
    } catch (error) {
      logger.error('Error validating quiz access:', error);
      res.status(500).json({
        error: 'Failed to validate access',
        code: 'VALIDATE_ACCESS_ERROR'
      });
    }
  }

  async getQuizStatistics(req, res) {
    try {
      const { quizId } = req.params;
      const stats = await QuizService.getQuizStatistics(quizId);

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error('Error getting quiz statistics:', error);
      res.status(500).json({
        error: 'Failed to get statistics',
        code: 'GET_STATS_ERROR'
      });
    }
  }
}

module.exports = new QuizController();
EOF

# Quiz Service
cat > domains/assessment/services/quiz-engine/src/services/QuizService.js << 'EOF'
const { Op } = require('sequelize');
const database = require('../../../../../shared/database/connection');
const logger = require('../utils/logger');

class QuizService {
  async getQuizById(quizId) {
    const sequelize = database.getSequelize();

    const quiz = await sequelize.query(`
      SELECT
        q.*,
        json_agg(
          json_build_object(
            'id', aq.id,
            'uuid', aq.uuid,
            'question_text', aq.question_text,
            'question_type', aq.question_type,
            'options', aq.options,
            'points', aq.points,
            'difficulty', aq.difficulty,
            'order_index', aq.order_index
          ) ORDER BY aq.order_index
        ) as questions
      FROM assessment_quizzes q
      LEFT JOIN assessment_questions aq ON q.id = aq.quiz_id
      WHERE q.id = :quizId AND q.status = 'published'
      GROUP BY q.id
    `, {
      replacements: { quizId },
      type: sequelize.QueryTypes.SELECT
    });

    return quiz[0] || null;
  }

  async getQuizForSession(quizId, userId) {
    const sequelize = database.getSequelize();

    // Get quiz with questions (without correct answers for security)
    const quiz = await sequelize.query(`
      SELECT
        q.id,
        q.uuid,
        q.title,
        q.description,
        q.time_limit,
        q.max_attempts,
        q.randomize_questions,
        q.randomize_answers,
        q.enable_proctoring,
        q.require_camera,
        q.require_microphone,
        json_agg(
          json_build_object(
            'id', aq.id,
            'uuid', aq.uuid,
            'question_text', aq.question_text,
            'question_type', aq.question_type,
            'options', aq.options,
            'points', aq.points,
            'difficulty', aq.difficulty,
            'order_index', aq.order_index,
            'explanation', aq.explanation
          ) ORDER BY aq.order_index
        ) as questions
      FROM assessment_quizzes q
      LEFT JOIN assessment_questions aq ON q.id = aq.quiz_id
      WHERE q.id = :quizId AND q.status = 'published'
      GROUP BY q.id
    `, {
      replacements: { quizId },
      type: sequelize.QueryTypes.SELECT
    });

    if (!quiz[0]) {
      throw new Error('Quiz not found');
    }

    let quizData = quiz[0];

    // Randomize questions if enabled
    if (quizData.randomize_questions) {
      quizData.questions = this.shuffleArray(quizData.questions);
    }

    // Randomize answer options if enabled
    if (quizData.randomize_answers) {
      quizData.questions = quizData.questions.map(question => {
        if (question.question_type === 'multiple_choice' && question.options) {
          question.options = this.shuffleArray(question.options);
        }
        return question;
      });
    }

    return quizData;
  }

  async validateAccess(quizId, userId) {
    const sequelize = database.getSequelize();

    // Check if quiz exists and is published
    const quiz = await sequelize.query(`
      SELECT
        id,
        title,
        max_attempts,
        available_from,
        available_until,
        status
      FROM assessment_quizzes
      WHERE id = :quizId
    `, {
      replacements: { quizId },
      type: sequelize.QueryTypes.SELECT
    });

    if (!quiz[0]) {
      return {
        allowed: false,
        reason: 'Quiz not found'
      };
    }

    const quizData = quiz[0];

    if (quizData.status !== 'published') {
      return {
        allowed: false,
        reason: 'Quiz not published'
      };
    }

    // Check time availability
    const now = new Date();
    if (quizData.available_from && new Date(quizData.available_from) > now) {
      return {
        allowed: false,
        reason: 'Quiz not yet available',
        available_from: quizData.available_from
      };
    }

    if (quizData.available_until && new Date(quizData.available_until) < now) {
      return {
        allowed: false,
        reason: 'Quiz no longer available',
        available_until: quizData.available_until
      };
    }

    // Check attempt limit
    const attempts = await sequelize.query(`
      SELECT COUNT(*) as attempt_count
      FROM assessment_sessions
      WHERE quiz_id = :quizId AND user_id = :userId AND status = 'submitted'
    `, {
      replacements: { quizId, userId },
      type: sequelize.QueryTypes.SELECT
    });

    const attemptCount = parseInt(attempts[0].attempt_count);

    if (quizData.max_attempts && attemptCount >= quizData.max_attempts) {
      return {
        allowed: false,
        reason: 'Maximum attempts exceeded',
        attempts_used: attemptCount,
        max_attempts: quizData.max_attempts
      };
    }

    return {
      allowed: true,
      attempts_used: attemptCount,
      max_attempts: quizData.max_attempts
    };
  }

  async getQuizStatistics(quizId) {
    const sequelize = database.getSequelize();

    const stats = await sequelize.query(`
      SELECT
        COUNT(DISTINCT s.user_id) as total_participants,
        COUNT(s.id) as total_attempts,
        AVG(r.percentage) as average_score,
        MIN(r.percentage) as min_score,
        MAX(r.percentage) as max_score,
        AVG(s.time_spent) as average_time,
        COUNT(CASE WHEN s.status = 'in_progress' THEN 1 END) as in_progress_count,
        COUNT(CASE WHEN s.status = 'submitted' THEN 1 END) as completed_count
      FROM assessment_sessions s
      LEFT JOIN assessment_results r ON s.id = r.session_id
      WHERE s.quiz_id = :quizId
    `, {
      replacements: { quizId },
      type: sequelize.QueryTypes.SELECT
    });

    return stats[0];
  }

  shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }
}

module.exports = new QuizService();
EOF
```

## 🔄 Phase 9: Event Bus & Communication (Week 6)

### **Step 9.1: Event Bus Service**
```bash
# Create Event Bus
mkdir -p gateway/event-bus/src/{brokers,handlers,models,config}

cat > gateway/event-bus/package.json << 'EOF'
{
  "name": "@ql-ctdt-v2/event-bus",
  "version": "1.0.0",
  "description": "Event Bus for QL_CTDT_V2",
  "main": "src/app.js",
  "scripts": {
    "start": "node src/app.js",
    "dev": "nodemon src/app.js"
  },
  "dependencies": {
    "express": "^4.18.2",
    "redis": "^4.6.5",
    "ioredis": "^5.3.1",
    "winston": "^3.8.2",
    "uuid": "^9.0.0",
    "joi": "^17.9.1"
  }
}
EOF

# Main Event Bus App
cat > gateway/event-bus/src/app.js << 'EOF'
const express = require('express');
const EventBus = require('./EventBus');
const logger = require('./utils/logger');

const app = express();
const PORT = process.env.EVENT_BUS_PORT || 8097;

app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Start Event Bus
async function startEventBus() {
  try {
    await EventBus.start();

    app.listen(PORT, () => {
      logger.info(`🚌 Event Bus running on port ${PORT}`);
    });
  } catch (error) {
    logger.error('Failed to start Event Bus:', error);
    process.exit(1);
  }
}

startEventBus();

module.exports = app;
EOF

# Event Bus Core
cat > gateway/event-bus/src/EventBus.js << 'EOF'
const Redis = require('ioredis');
const EventEmitter = require('events');
const logger = require('./utils/logger');
const EventValidator = require('./handlers/EventValidator');
const EventRouter = require('./handlers/EventRouter');

class EventBus extends EventEmitter {
  constructor() {
    super();
    this.redis = null;
    this.subscriber = null;
    this.publisher = null;
    this.isRunning = false;
    this.eventHandlers = new Map();
    this.eventStats = {
      published: 0,
      consumed: 0,
      errors: 0
    };
  }

  async start() {
    try {
      // Initialize Redis connections
      this.redis = new Redis({
        host: process.env.REDIS_HOST || 'localhost',
        port: process.env.REDIS_PORT || 6379,
        password: process.env.REDIS_PASSWORD,
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3
      });

      this.subscriber = this.redis.duplicate();
      this.publisher = this.redis.duplicate();

      // Setup event channels
      await this.setupChannels();

      // Start listening for events
      await this.startListening();

      this.isRunning = true;
      logger.info('✅ Event Bus started successfully');

    } catch (error) {
      logger.error('❌ Failed to start Event Bus:', error);
      throw error;
    }
  }

  async stop() {
    this.isRunning = false;

    if (this.subscriber) {
      await this.subscriber.disconnect();
    }

    if (this.publisher) {
      await this.publisher.disconnect();
    }

    if (this.redis) {
      await this.redis.disconnect();
    }

    logger.info('🛑 Event Bus stopped');
  }

  async setupChannels() {
    const channels = [
      'assessment.events',
      'practice.events',
      'content.events',
      'analytics.events',
      'system.events'
    ];

    for (const channel of channels) {
      await this.subscriber.subscribe(channel);
      logger.info(`📡 Subscribed to channel: ${channel}`);
    }
  }

  async startListening() {
    this.subscriber.on('message', async (channel, message) => {
      try {
        const event = JSON.parse(message);
        await this.handleEvent(channel, event);
      } catch (error) {
        logger.error(`Error processing event from ${channel}:`, error);
        this.eventStats.errors++;
      }
    });

    this.subscriber.on('error', (error) => {
      logger.error('Redis subscriber error:', error);
    });
  }

  async handleEvent(channel, event) {
    try {
      // Validate event structure
      const validation = EventValidator.validate(event);
      if (!validation.isValid) {
        logger.warn(`Invalid event structure:`, validation.errors);
        return;
      }

      // Add metadata
      event.metadata = {
        ...event.metadata,
        receivedAt: new Date().toISOString(),
        channel: channel,
        processedBy: 'event-bus'
      };

      // Route event to appropriate handlers
      await EventRouter.route(event);

      // Update stats
      this.eventStats.consumed++;

      // Emit for local listeners
      this.emit('event', event);

      logger.debug(`📨 Processed event: ${event.type}`, {
        eventId: event.id,
        type: event.type,
        channel: channel
      });

    } catch (error) {
      logger.error('Error handling event:', error);
      this.eventStats.errors++;
    }
  }

  async publish(channel, event) {
    try {
      if (!this.isRunning) {
        throw new Error('Event Bus is not running');
      }

      // Validate event
      const validation = EventValidator.validate(event);
      if (!validation.isValid) {
        throw new Error(`Invalid event: ${validation.errors.join(', ')}`);
      }

      // Add publish metadata
      event.metadata = {
        ...event.metadata,
        publishedAt: new Date().toISOString(),
        publishedBy: 'event-bus'
      };

      // Publish to Redis
      await this.publisher.publish(channel, JSON.stringify(event));

      // Update stats
      this.eventStats.published++;

      logger.debug(`📤 Published event: ${event.type}`, {
        eventId: event.id,
        type: event.type,
        channel: channel
      });

    } catch (error) {
      logger.error('Error publishing event:', error);
      this.eventStats.errors++;
      throw error;
    }
  }

  // Register event handler
  registerHandler(eventType, handler) {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }

    this.eventHandlers.get(eventType).push(handler);
    logger.info(`🔧 Registered handler for event type: ${eventType}`);
  }

  // Get event statistics
  getStats() {
    return {
      ...this.eventStats,
      isRunning: this.isRunning,
      registeredHandlers: this.eventHandlers.size,
      uptime: process.uptime()
    };
  }
}

module.exports = new EventBus();
EOF
```

## 📊 Phase 10: Testing & Deployment (Week 7-9)

### **Step 10.1: Testing Strategy**
```bash
# Create test structure
mkdir -p tests/{unit,integration,e2e,load}

# Integration test example
cat > tests/integration/quiz-flow.test.js << 'EOF'
const request = require('supertest');
const { setupTestDB, cleanupTestDB } = require('../helpers/database');

describe('Complete Quiz Flow Integration', () => {
  let authToken;
  let quizId;
  let sessionId;

  beforeAll(async () => {
    await setupTestDB();

    // Login and get auth token
    const loginResponse = await request('http://localhost:8080')
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      });

    authToken = loginResponse.body.token;
  });

  afterAll(async () => {
    await cleanupTestDB();
  });

  describe('Assessment Flow', () => {
    it('should create quiz session via PIN', async () => {
      const response = await request('http://localhost:8080')
        .post('/api/quiz/join-by-pin')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ pin: 'TEST01' });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.redirectUrl).toContain('localhost:3001');

      sessionId = response.body.data.sessionId;
    });

    it('should load quiz in assessment UI', async () => {
      const response = await request('http://localhost:3001')
        .get(`/api/quiz/session/${sessionId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data.quiz).toBeDefined();
      expect(response.body.data.session).toBeDefined();
    });

    it('should submit answers and complete quiz', async () => {
      // Submit answer
      const answerResponse = await request('http://localhost:3001')
        .post(`/api/answer/submit`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          sessionId,
          questionId: 1,
          answer: { selectedOption: 'A' }
        });

      expect(answerResponse.status).toBe(200);

      // Complete quiz
      const completeResponse = await request('http://localhost:3001')
        .post(`/api/quiz/complete`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ sessionId });

      expect(completeResponse.status).toBe(200);
      expect(completeResponse.body.data.redirectUrl).toContain('localhost:3000');
    });
  });

  describe('Practice Flow', () => {
    it('should handle practice session with gamification', async () => {
      const response = await request('http://localhost:8080')
        .post('/api/quiz/join-by-pin')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ pin: 'PRAC01' });

      expect(response.status).toBe(200);
      expect(response.body.data.redirectUrl).toContain('localhost:3002');
    });

    it('should award currency and XP', async () => {
      const response = await request('http://localhost:3002')
        .post('/api/practice/complete-question')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          sessionId,
          questionId: 1,
          answer: { selectedOption: 'A' },
          isCorrect: true
        });

      expect(response.status).toBe(200);
      expect(response.body.data.rewards).toBeDefined();
      expect(response.body.data.rewards.xp).toBeGreaterThan(0);
      expect(response.body.data.rewards.coins).toBeGreaterThan(0);
    });
  });
});
EOF

# Load test example
cat > tests/load/concurrent-users.js << 'EOF'
const { check } = require('k6');
const http = require('k6/http');

export let options = {
  stages: [
    { duration: '2m', target: 100 }, // Ramp up to 100 users
    { duration: '5m', target: 100 }, // Stay at 100 users
    { duration: '2m', target: 200 }, // Ramp up to 200 users
    { duration: '5m', target: 200 }, // Stay at 200 users
    { duration: '2m', target: 0 },   // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests must complete below 500ms
    http_req_failed: ['rate<0.1'],    // Error rate must be below 10%
  },
};

export default function () {
  // Test quiz taking flow
  let response = http.post('http://localhost:8080/api/quiz/join-by-pin', {
    pin: 'LOAD01'
  });

  check(response, {
    'quiz join successful': (r) => r.status === 200,
    'response time < 500ms': (r) => r.timings.duration < 500,
  });

  // Simulate quiz taking
  if (response.status === 200) {
    const sessionId = response.json().data.sessionId;

    // Submit answers
    for (let i = 0; i < 10; i++) {
      let answerResponse = http.post(`http://localhost:3001/api/answer/submit`, {
        sessionId: sessionId,
        questionId: i + 1,
        answer: { selectedOption: 'A' }
      });

      check(answerResponse, {
        'answer submit successful': (r) => r.status === 200,
      });
    }
  }
}
EOF
```

### **Step 10.2: Deployment Scripts**
```bash
# Production deployment script
cat > scripts/deploy.sh << 'EOF'
#!/bin/bash

set -e

echo "🚀 Deploying QL_CTDT_V2 to production..."

# Build all services
echo "📦 Building services..."
docker-compose -f docker-compose.prod.yml build

# Run database migrations
echo "🗄️ Running database migrations..."
./scripts/migrate.sh

# Deploy with zero downtime
echo "🔄 Deploying with zero downtime..."
docker-compose -f docker-compose.prod.yml up -d

# Health check
echo "🏥 Running health checks..."
sleep 30

services=(
  "http://localhost:8080/health"
  "http://localhost:3000"
  "http://localhost:3001"
  "http://localhost:3002"
)

for service in "${services[@]}"; do
  if curl -f "$service" > /dev/null 2>&1; then
    echo "✅ $service is healthy"
  else
    echo "❌ $service is not responding"
    exit 1
  fi
done

echo "✅ Deployment completed successfully!"
EOF

chmod +x scripts/deploy.sh

# Monitoring setup
cat > infrastructure/monitoring/prometheus.yml << 'EOF'
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'api-gateway'
    static_configs:
      - targets: ['api-gateway:8080']
    metrics_path: '/metrics'

  - job_name: 'assessment-services'
    static_configs:
      - targets:
        - 'assessment-quiz-engine:8081'
        - 'assessment-proctoring:8082'
        - 'assessment-grading:8083'

  - job_name: 'practice-services'
    static_configs:
      - targets:
        - 'practice-quiz-engine:8084'
        - 'practice-gamification:8085'
        - 'practice-currency:8086'
        - 'practice-skills:8087'

  - job_name: 'content-services'
    static_configs:
      - targets:
        - 'content-quiz-builder:8089'
        - 'content-question-bank:8090'

  - job_name: 'analytics-services'
    static_configs:
      - targets:
        - 'analytics-reporting:8094'
        - 'analytics-realtime:8096'
EOF
```

## 🎯 Final Implementation Checklist

### **Phase 1-3: Foundation (Week 1)**
- [ ] ✅ Project structure setup
- [ ] ✅ Environment configuration
- [ ] ✅ Docker infrastructure
- [ ] ✅ Database schemas
- [ ] ✅ Shared components

### **Phase 4-5: Gateway & Portal (Week 2)**
- [ ] ✅ API Gateway with service discovery
- [ ] ✅ Web Portal with Next.js
- [ ] ✅ Cross-app navigation
- [ ] ✅ Authentication system

### **Phase 6-7: Frontend Apps (Week 3)**
- [ ] ✅ Assessment UI (React SPA)
- [ ] ✅ Practice UI with gamification
- [ ] ✅ Teacher dashboards
- [ ] ✅ Real-time features

### **Phase 8: Backend Services (Week 4-6)**
- [ ] ✅ Assessment services (quiz, proctoring, grading)
- [ ] ✅ Practice services (quiz, gamification, currency, skills)
- [ ] ✅ Content services (quiz builder, question bank)
- [ ] ✅ Analytics services (data, reporting, insights)

### **Phase 9: Integration (Week 6)**
- [ ] ✅ Event Bus implementation
- [ ] ✅ Service-to-service communication
- [ ] ✅ Real-time features
- [ ] ✅ Cross-domain session management

### **Phase 10: Testing & Deployment (Week 7-9)**
- [ ] ✅ Unit tests for all services
- [ ] ✅ Integration tests for user flows
- [ ] ✅ Load testing for performance
- [ ] ✅ Production deployment scripts
- [ ] ✅ Monitoring and alerting

## 🚀 Getting Started

1. **Clone and setup**:
```bash
git clone <your-repo>
cd QL_CTDT_V2
./scripts/setup.sh
```

2. **Start development**:
```bash
./scripts/dev.sh
```

3. **Access applications**:
- Main Portal: http://localhost:3000
- Assessment UI: http://localhost:3001
- Practice UI: http://localhost:3002
- API Gateway: http://localhost:8080

4. **Monitor services**:
- Prometheus: http://localhost:9090
- Grafana: http://localhost:3001

**Chúc bạn implementation thành công! 🎉**
