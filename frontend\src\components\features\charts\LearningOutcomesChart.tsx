"use client";

import React, { useState, useEffect } from "react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Bar } from "react-chartjs-2";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON>Header,
  CardTitle,
} from "@/components/ui/layout";
import { Button } from "@/components/ui/forms";
import { Badge } from "@/components/ui/feedback";
import { Progress } from "@/components/ui/feedback";
import {
  Loader2,
  Target,
  TrendingUp,
  BarChart3,
  BookOpen,
  Award,
  Users,
  CheckCircle,
  AlertTriangle,
} from "lucide-react";
import { chapterAnalyticsService } from "@/lib/services/api/chapter-analytics.service";
import {
  LearningOutcomesChartResponse,
  LearningOutcomeDetailData,
} from "@/lib/types/chapter-analytics";
import { showErrorToast } from "@/lib/utils/toast-utils";

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface LearningOutcomesChartProps {
  quizId: number;
  quizName?: string;
  className?: string;
}

export default function LearningOutcomesChart({
  quizId,
  quizName = "Quiz",
  className = "",
}: LearningOutcomesChartProps) {
  // States
  const [chartData, setChartData] = useState<LearningOutcomesChartResponse | null>(null);
  const [selectedLO, setSelectedLO] = useState<number | null>(null);
  const [detailData, setDetailData] = useState<LearningOutcomeDetailData | null>(null);
  const [loading, setLoading] = useState(true);
  const [detailLoading, setDetailLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch chart data (Level 1)
  const fetchChartData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await chapterAnalyticsService.getLearningOutcomesChart(quizId);
      setChartData(response);
    } catch (error) {
      console.error("Error fetching LO chart data:", error);
      const errorMessage = error instanceof Error ? error.message : "Không thể tải dữ liệu biểu đồ";
      setError(errorMessage);
      showErrorToast(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Fetch LO detail data (Level 2)
  const fetchLODetail = async (loId: number) => {
    try {
      setDetailLoading(true);
      const response = await chapterAnalyticsService.getLearningOutcomeDetail(quizId, loId);
      setDetailData(response);
    } catch (error) {
      console.error("Error fetching LO detail:", error);
      showErrorToast("Không thể tải chi tiết Learning Outcome");
    } finally {
      setDetailLoading(false);
    }
  };

  useEffect(() => {
    if (quizId) {
      fetchChartData();
    }
  }, [quizId]);

  // Handle bar click (Level 1 -> Level 2)
  const handleBarClick = (loId: number) => {
    setSelectedLO(loId);
    fetchLODetail(loId);
  };



  // Helper functions
  const getPerformanceColor = (level: string): string => {
    switch (level) {
      case "excellent": return "#4CAF50";
      case "good": return "#2196F3";
      case "average": return "#FF9800";
      case "weak": return "#F44336";
      default: return "#9E9E9E";
    }
  };

  const getPerformanceBadgeColor = (level: string): string => {
    switch (level) {
      case "excellent": return "text-green-600 bg-green-100";
      case "good": return "text-blue-600 bg-blue-100";
      case "average": return "text-yellow-600 bg-yellow-100";
      case "weak": return "text-red-600 bg-red-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getPerformanceText = (level: string): string => {
    switch (level) {
      case "excellent": return "Xuất sắc";
      case "good": return "Khá";
      case "average": return "Trung bình";
      case "weak": return "Yếu";
      default: return "Chưa xác định";
    }
  };

  // Loading state
  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-3 mx-auto" />
            <p className="text-muted-foreground">Đang tải dữ liệu biểu đồ...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (error) {
    return (
      <Card className={className}>
        <CardContent className="flex flex-col items-center py-8">
          <AlertTriangle className="h-16 w-16 text-red-500 mb-4" />
          <p className="text-lg font-medium text-red-600 mb-2">Lỗi tải dữ liệu</p>
          <p className="text-muted-foreground text-center mb-4">{error}</p>
          <Button onClick={fetchChartData} variant="outline">
            Thử lại
          </Button>
        </CardContent>
      </Card>
    );
  }

  // No data state
  if (!chartData) {
    return (
      <Card className={className}>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <BookOpen className="h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold text-gray-600 mb-2">Chưa có dữ liệu</h3>
          <p className="text-muted-foreground">Chưa có dữ liệu Learning Outcomes cho quiz này</p>
        </CardContent>
      </Card>
    );
  }

  // Get chart data
  const learningOutcomes = chartData.chart_data || [];

  // Prepare Chart.js data
  const chartJSData = {
    labels: learningOutcomes.map((lo) => lo.short_name),
    datasets: [
      {
        label: "Độ chính xác (%)",
        data: learningOutcomes.map((lo) => lo.accuracy || 0),
        backgroundColor: learningOutcomes.map((lo) => getPerformanceColor(lo.performance_level)),
        borderColor: learningOutcomes.map((lo) => getPerformanceColor(lo.performance_level)),
        borderWidth: 1,
        borderRadius: 4,
        borderSkipped: false,
      },
    ],
  };

  // Chart.js options with click handler
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: function (context: any) {
            const lo = learningOutcomes[context.dataIndex];
            return [
              `Độ chính xác: ${context.parsed.y.toFixed(1)}%`,
              `Câu hỏi: ${lo.total_questions}`,
              `Đúng: ${lo.correct_answers}/${lo.total_attempts}`,
              `Mức độ: ${getPerformanceText(lo.performance_level)}`,
            ];
          },
        },
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
        ticks: {
          stepSize: 20,
          callback: function(value: any) {
            return value + '%';
          }
        },
        title: {
          display: true,
          text: "Độ chính xác (%)",
        },
      },
      x: {
        title: {
          display: true,
          text: "Learning Outcomes",
        },
      },
    },
    onClick: (_event: any, elements: any) => {
      if (elements.length > 0) {
        const clickedIndex = elements[0].index;
        const clickedLO = learningOutcomes[clickedIndex];
        if (clickedLO) {
          handleBarClick(clickedLO.lo_id);
        }
      }
    },
  };

  // Main Chart View với Detail dưới (như StudentGroupBarChart)
  return (
    <div className={`space-y-6 ${className}`}>
      {/* Bar Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-6 w-6 text-primary" />
            Biểu đồ Learning Outcomes - {quizName}
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Click vào cột để xem chi tiết Learning Outcome
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Summary Stats */}
            {chartData && (
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Target className="h-5 w-5 text-blue-600" />
                    <span className="font-medium text-blue-900">Tổng LOs</span>
                  </div>
                  <div className="text-2xl font-bold text-blue-600">
                    {chartData.summary.total_los}
                  </div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <TrendingUp className="h-5 w-5 text-green-600" />
                    <span className="font-medium text-green-900">Độ chính xác TB</span>
                  </div>
                  <div className="text-2xl font-bold text-green-600">
                    {chartData.summary.average_accuracy.toFixed(1)}%
                  </div>
                </div>
                <div className="bg-emerald-50 p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Award className="h-5 w-5 text-emerald-600" />
                    <span className="font-medium text-emerald-900">LO mạnh nhất</span>
                  </div>
                  <div className="text-sm font-bold text-emerald-600">
                    {chartData.summary.strongest_lo.lo_name}
                  </div>
                  <div className="text-xs text-emerald-600">
                    {chartData.summary.strongest_lo.accuracy.toFixed(1)}%
                  </div>
                </div>
                <div className="bg-red-50 p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertTriangle className="h-5 w-5 text-red-600" />
                    <span className="font-medium text-red-900">LO cần cải thiện</span>
                  </div>
                  <div className="text-sm font-bold text-red-600">
                    {chartData.summary.weakest_lo.lo_name}
                  </div>
                  <div className="text-xs text-red-600">
                    {chartData.summary.weakest_lo.accuracy.toFixed(1)}%
                  </div>
                </div>
              </div>
            )}

            {/* Chart.js Bar Chart */}
            <div className="h-64">
              <Bar data={chartJSData} options={chartOptions} />
            </div>

            {/* Legend with Selection Indicator */}
            <div className="flex flex-wrap justify-center gap-4 pt-4 border-t">
              {learningOutcomes.map((lo) => (
                <div
                  key={lo.lo_id}
                  className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all cursor-pointer ${
                    selectedLO === lo.lo_id
                      ? "bg-primary/10 ring-2 ring-primary/20"
                      : "hover:bg-gray-50"
                  }`}
                  onClick={() => handleBarClick(lo.lo_id)}
                >
                  <div
                    className="w-4 h-4 rounded"
                    style={{ backgroundColor: getPerformanceColor(lo.performance_level) }}
                  />
                  <span
                    className={`text-sm ${
                      selectedLO === lo.lo_id
                        ? "font-medium text-primary"
                        : ""
                    }`}
                  >
                    {lo.short_name}: {lo.accuracy.toFixed(1)}%
                  </span>
                  {selectedLO === lo.lo_id && (
                    <CheckCircle className="h-4 w-4 text-primary" />
                  )}
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Selected LO Details (hiển thị dưới chart) */}
      {selectedLO && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-6 w-6 text-primary" />
              Chi tiết Learning Outcome
              {detailLoading && <Loader2 className="h-4 w-4 animate-spin" />}
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              Thông tin chi tiết về Learning Outcome đã chọn
            </p>
          </CardHeader>
          <CardContent>
            {detailLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-center">
                  <Loader2 className="h-8 w-8 animate-spin text-primary mb-3 mx-auto" />
                  <p className="text-muted-foreground">
                    Đang tải chi tiết Learning Outcome...
                  </p>
                </div>
              </div>
            ) : detailData ? (
              <div className="space-y-6">
                {/* LO Info Header */}
                <div className="flex items-center justify-between p-4 bg-muted/20 rounded-lg">
                  <div>
                    <h3 className="text-lg font-semibold">{detailData.lo_info.lo_name}</h3>
                    <p className="text-sm text-muted-foreground">
                      {detailData.lo_info.description}
                    </p>
                  </div>
                  <Badge className={getPerformanceBadgeColor(detailData.lo_info.performance_level)}>
                    {getPerformanceText(detailData.lo_info.performance_level)}
                  </Badge>
                </div>

                {/* LO Overview */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Target className="h-5 w-5 text-blue-600" />
                      <span className="font-medium text-blue-900">Độ chính xác</span>
                    </div>
                    <div className="text-2xl font-bold text-blue-600">
                      {detailData.lo_info.accuracy.toFixed(1)}%
                    </div>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <BookOpen className="h-5 w-5 text-green-600" />
                      <span className="font-medium text-green-900">Tổng câu hỏi</span>
                    </div>
                    <div className="text-2xl font-bold text-green-600">
                      {detailData.lo_info.total_questions}
                    </div>
                  </div>
                </div>

                {/* Question Breakdown */}
                <div>
                  <h4 className="font-semibold mb-4 flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Phân tích theo câu hỏi
                  </h4>
                  <div className="space-y-3">
                    {Array.isArray(detailData.question_breakdown) ?
                      detailData.question_breakdown.map((question: any, index: number) => (
                        <div key={question.question_id || index} className="border rounded-lg p-4">
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex-1">
                              <p className="font-medium">Câu {index + 1}</p>
                              <p className="text-sm text-muted-foreground line-clamp-2">
                                {question.question_text}
                              </p>
                            </div>
                            <Badge variant="outline">
                              {question.difficulty}
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                            <span>Độ chính xác: {question.accuracy?.toFixed(1) || 0}%</span>
                            <span>{question.correct_count || 0}/{question.total_attempts || 0} đúng</span>
                          </div>
                          <Progress
                            value={question.accuracy || 0}
                            className="h-2 mt-2"
                          />
                        </div>
                      )) : (
                        <p className="text-muted-foreground">Không có dữ liệu câu hỏi</p>
                      )
                    }
                  </div>
                </div>

                {/* Student Performance */}
                <div>
                  <h4 className="font-semibold mb-4 flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Phân tích theo nhóm học sinh
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {Array.isArray(detailData.student_performance) ?
                      detailData.student_performance.map((group: any, index: number) => (
                        <div key={group.performance_level || index} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <Badge className={getPerformanceBadgeColor(group.performance_level)}>
                              {getPerformanceText(group.performance_level)}
                            </Badge>
                            <span className="font-bold">{group.student_count || 0}</span>
                          </div>
                          <div className="space-y-1">
                            {Array.isArray(group.students) ?
                              group.students.slice(0, 3).map((student: any, idx: number) => (
                                <div key={student.user_id || idx} className="text-xs text-muted-foreground">
                                  {student.name} ({student.accuracy?.toFixed(1) || 0}%)
                                </div>
                              )) : null
                            }
                            {Array.isArray(group.students) && group.students.length > 3 && (
                              <div className="text-xs text-muted-foreground">
                                +{group.students.length - 3} học sinh khác
                              </div>
                            )}
                          </div>
                        </div>
                      )) : (
                        <p className="text-muted-foreground col-span-full">Không có dữ liệu học sinh</p>
                      )
                    }
                  </div>
                </div>

                {/* Insights & Recommendations */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-3 flex items-center gap-2">
                      <TrendingUp className="h-5 w-5 text-blue-600" />
                      Nhận xét
                    </h4>
                    <div className="space-y-2">
                      {Array.isArray(detailData.insights) ?
                        detailData.insights.map((insight: string, index: number) => (
                          <div key={index} className="bg-blue-50 p-3 rounded-lg text-sm">
                            {insight}
                          </div>
                        )) : (
                          <p className="text-muted-foreground">Không có nhận xét</p>
                        )
                      }
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-3 flex items-center gap-2">
                      <Award className="h-5 w-5 text-green-600" />
                      Khuyến nghị
                    </h4>
                    <div className="space-y-2">
                      {Array.isArray(detailData.recommendations) ?
                        detailData.recommendations.map((rec: any, index: number) => (
                          <div key={index} className="bg-green-50 p-3 rounded-lg">
                            <div className="flex items-start justify-between">
                              <p className="text-sm flex-1">{rec.suggestion}</p>
                              <Badge
                                variant="outline"
                                className={`ml-2 ${
                                  rec.priority === 'high' ? 'border-red-500 text-red-700' :
                                  rec.priority === 'medium' ? 'border-orange-500 text-orange-700' :
                                  'border-green-500 text-green-700'
                                }`}
                              >
                                {rec.priority === 'high' ? 'Cao' :
                                 rec.priority === 'medium' ? 'Trung bình' : 'Thấp'}
                              </Badge>
                            </div>
                          </div>
                        )) : (
                          <p className="text-muted-foreground">Không có khuyến nghị</p>
                        )
                      }
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <BookOpen className="h-12 w-12 text-muted-foreground mb-3" />
                <p className="text-muted-foreground">
                  Không có dữ liệu chi tiết cho Learning Outcome này
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
