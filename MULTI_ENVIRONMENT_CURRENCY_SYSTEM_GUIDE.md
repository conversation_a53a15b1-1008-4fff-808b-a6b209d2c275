k# 💰 Multi-Environment Currency System Implementation Guide

## 📋 Overview
Hệ thống Currency được thiết kế để hoạt động trong môi trường multi-branch với Single Database + Branch Flags approach. Currency system chỉ hoạt động ở nhánh `devgame` (practice mode), không có ở `main` (production) và `dev` (evaluation).

## 🏗️ Architecture Design

### Branch Strategy
```
┌─────────────────────────────────────────────────────────┐
│                    Single Database                      │
│                      ql_ctdt                           │
├─────────────────┬─────────────────┬─────────────────────┤
│   main branch   │   dev branch    │   devgame branch    │
│  (production)   │  (evaluation)   │    (practice)       │
│                 │                 │                     │
│ ❌ No Currency  │ ❌ No Currency  │ ✅ Currency System  │
│ ❌ No Gaming    │ ❌ No Gaming    │ ✅ Gaming Features  │
│ Real scores     │ Real scores     │ Practice scores     │
└─────────────────┴─────────────────┴─────────────────────┘
```

### Data Isolation Strategy
- **Single Database**: `ql_ctdt`
- **Branch Identification**: Column `branch_mode` trong các bảng
- **Currency Tables**: Chỉ có data cho `branch_mode = 'devgame'`
- **API Availability**: Currency endpoints chỉ response ở devgame environment

## 🗄️ Database Schema

### Core Tables Structure
```sql
-- Users table với branch identification
ALTER TABLE users ADD COLUMN IF NOT EXISTS branch_mode VARCHAR(20) DEFAULT 'dev';
CREATE INDEX IF NOT EXISTS idx_users_branch_mode ON users(branch_mode);

-- Currency system tables (chỉ cho devgame)
CREATE TABLE IF NOT EXISTS user_currency (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    coins INTEGER DEFAULT 0 CHECK (coins >= 0),
    gems INTEGER DEFAULT 0 CHECK (gems >= 0),
    branch_mode VARCHAR(20) DEFAULT 'devgame' CHECK (branch_mode = 'devgame'),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    UNIQUE(user_id, branch_mode)
);

-- Currency transaction history
CREATE TABLE IF NOT EXISTS currency_transactions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    transaction_type VARCHAR(20) NOT NULL, -- 'earn', 'spend', 'admin_adjust'
    currency_type VARCHAR(10) NOT NULL,    -- 'coins', 'gems'
    amount INTEGER NOT NULL,               -- Positive for earn, negative for spend
    balance_before INTEGER NOT NULL,
    balance_after INTEGER NOT NULL,
    source VARCHAR(50),                    -- 'quiz_completion', 'daily_login', 'purchase', etc.
    description TEXT,
    branch_mode VARCHAR(20) DEFAULT 'devgame' CHECK (branch_mode = 'devgame'),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_user_currency_user_branch ON user_currency(user_id, branch_mode);
CREATE INDEX idx_currency_transactions_user_branch ON currency_transactions(user_id, branch_mode);
CREATE INDEX idx_currency_transactions_type ON currency_transactions(transaction_type);
```

## 🔧 Backend Configuration

### Environment Setup
```javascript
// backend/src/config/branch.js
const BRANCH_MODE = process.env.BRANCH_MODE || 'dev';

const BRANCH_CONFIG = {
  main: {
    name: 'production',
    description: 'Production environment for real assessments',
    enableCurrency: false,
    enableGameFeatures: false,
    dataFilter: { branch_mode: 'main' },
    strictMode: true
  },
  dev: {
    name: 'evaluation',
    description: 'Development environment for accurate evaluation',
    enableCurrency: false,
    enableGameFeatures: false,
    dataFilter: { branch_mode: 'dev' },
    strictMode: true
  },
  devgame: {
    name: 'practice',
    description: 'Gaming environment for practice and skill development',
    enableCurrency: true,
    enableGameFeatures: true,
    dataFilter: { branch_mode: 'devgame' },
    strictMode: false,
    currencyConfig: {
      startingCoins: 100,
      startingGems: 10,
      dailyLoginBonus: { coins: 50, gems: 2 },
      quizCompletionReward: { coins: 25, gems: 1 }
    }
  }
};

const getCurrentBranchConfig = () => BRANCH_CONFIG[BRANCH_MODE];
const isCurrencyEnabled = () => getCurrentBranchConfig().enableCurrency;

module.exports = {
  BRANCH_CONFIG,
  BRANCH_MODE,
  getCurrentBranchConfig,
  isCurrencyEnabled,
  current: getCurrentBranchConfig()
};
```

## 🌐 API Implementation

### Currency Routes Protection
```javascript
// backend/src/middleware/currencyGuard.js
const { isCurrencyEnabled, BRANCH_MODE } = require('../config/branch');

const currencyGuard = (req, res, next) => {
  if (!isCurrencyEnabled()) {
    return res.status(404).json({
      error: 'Currency system not available',
      branch: BRANCH_MODE,
      message: 'Currency features are only available in practice mode (devgame)',
      availableIn: ['devgame']
    });
  }
  next();
};

module.exports = currencyGuard;
```

### GET /api/currency/balance
```javascript
// backend/src/routes/currencyRoutes.js
const express = require('express');
const router = express.Router();
const currencyGuard = require('../middleware/currencyGuard');
const UserCurrency = require('../models/UserCurrency');

// Apply currency guard to all routes
router.use(currencyGuard);

router.get('/balance', async (req, res) => {
  try {
    const userId = req.user.id;

    let balance = await UserCurrency.findOne({
      where: {
        user_id: userId,
        branch_mode: 'devgame'
      }
    });

    // Create initial balance if not exists
    if (!balance) {
      const { currencyConfig } = require('../config/branch').current;
      balance = await UserCurrency.create({
        user_id: userId,
        coins: currencyConfig.startingCoins,
        gems: currencyConfig.startingGems,
        branch_mode: 'devgame'
      });
    }

    res.json({
      success: true,
      data: {
        coins: balance.coins,
        gems: balance.gems,
        last_updated: balance.updated_at
      },
      branch: 'devgame',
      environment: 'practice'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
```

## 🐳 Docker Configuration

### Multi-Environment Setup
```yaml
# docker-compose.multi-env.yml
version: '3.8'

services:
  # Shared Database
  postgres:
    image: postgres:15
    container_name: ql_ctdt_shared_db
    environment:
      POSTGRES_DB: ql_ctdt
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init-multi-env.sql:/docker-entrypoint-initdb.d/init.sql

  # Shared Redis
  redis:
    image: redis:7-alpine
    container_name: ql_ctdt_shared_redis
    ports:
      - "6379:6379"

  # Production Environment (main branch)
  main-backend:
    build: ./backend
    container_name: ql_ctdt_main_backend
    environment:
      - NODE_ENV=production
      - BRANCH_MODE=main
      - DB_HOST=postgres
      - DB_NAME=ql_ctdt
      - REDIS_HOST=redis
      - REDIS_DB=0
    ports:
      - "8888:8888"
    depends_on:
      - postgres
      - redis

  main-frontend:
    build: ./frontend
    container_name: ql_ctdt_main_frontend
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_BRANCH_MODE=main
      - NEXT_PUBLIC_API_URL=http://localhost:8888
    ports:
      - "3000:3000"
    depends_on:
      - main-backend

  # Development Environment (dev branch)
  dev-backend:
    build: ./backend
    container_name: ql_ctdt_dev_backend
    environment:
      - NODE_ENV=development
      - BRANCH_MODE=dev
      - DB_HOST=postgres
      - DB_NAME=ql_ctdt
      - REDIS_HOST=redis
      - REDIS_DB=1
    ports:
      - "8889:8888"
    depends_on:
      - postgres
      - redis

  dev-frontend:
    build: ./frontend
    container_name: ql_ctdt_dev_frontend
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_BRANCH_MODE=dev
      - NEXT_PUBLIC_API_URL=http://localhost:8889
    ports:
      - "3001:3000"
    depends_on:
      - dev-backend

  # Game Development Environment (devgame branch)
  game-backend:
    build: ./backend
    container_name: ql_ctdt_game_backend
    environment:
      - NODE_ENV=development
      - BRANCH_MODE=devgame
      - DB_HOST=postgres
      - DB_NAME=ql_ctdt
      - REDIS_HOST=redis
      - REDIS_DB=2
    ports:
      - "8890:8888"
    depends_on:
      - postgres
      - redis

  game-frontend:
    build: ./frontend
    container_name: ql_ctdt_game_frontend
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_BRANCH_MODE=devgame
      - NEXT_PUBLIC_API_URL=http://localhost:8890
      - NEXT_PUBLIC_ENABLE_CURRENCY=true
    ports:
      - "3002:3000"
    depends_on:
      - game-backend

volumes:
  postgres_data:
```

## 🚀 Deployment Guide

### Step 1: Database Initialization
```sql
-- database/init-multi-env.sql
-- Create branch-aware indexes
CREATE INDEX IF NOT EXISTS idx_users_branch_mode ON users(branch_mode);

-- Initialize currency tables
CREATE TABLE IF NOT EXISTS user_currency (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    coins INTEGER DEFAULT 0 CHECK (coins >= 0),
    gems INTEGER DEFAULT 0 CHECK (gems >= 0),
    branch_mode VARCHAR(20) DEFAULT 'devgame' CHECK (branch_mode = 'devgame'),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_id, branch_mode)
);

-- Grant permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;
```

### Step 2: Deploy All Environments
```bash
# Build and start all services
docker-compose -f docker-compose.multi-env.yml up -d

# Verify services are running
docker-compose -f docker-compose.multi-env.yml ps

# Check logs
docker-compose -f docker-compose.multi-env.yml logs -f game-backend
```

### Step 3: Test Currency API
```bash
# Test main branch (should return 404)
curl -X GET http://localhost:8888/api/currency/balance \
  -H "Authorization: Bearer YOUR_TOKEN"

# Test dev branch (should return 404)
curl -X GET http://localhost:8889/api/currency/balance \
  -H "Authorization: Bearer YOUR_TOKEN"

# Test devgame branch (should return balance)
curl -X GET http://localhost:8890/api/currency/balance \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 📊 Analytics & Monitoring

### Branch Performance Comparison
```javascript
// backend/src/controllers/analyticsController.js
router.get('/branch-comparison', async (req, res) => {
  const results = await sequelize.query(`
    SELECT
      branch_mode,
      COUNT(DISTINCT user_id) as total_users,
      AVG(score) as avg_score,
      COUNT(*) as total_attempts
    FROM quiz_attempts
    WHERE created_at >= NOW() - INTERVAL '30 days'
    GROUP BY branch_mode
  `, { type: QueryTypes.SELECT });

  res.json({
    success: true,
    data: results,
    analysis: {
      main: 'Production - Real assessment data',
      dev: 'Evaluation - Accurate scoring for assessment',
      devgame: 'Practice - May include skill-enhanced performance'
    }
  });
});
```

## 🔍 Testing Strategy

### Environment-Specific Tests
```javascript
// tests/currency.test.js
describe('Currency System - Multi Environment', () => {
  describe('Branch Protection', () => {
    it('should deny currency access in main branch', async () => {
      process.env.BRANCH_MODE = 'main';
      const response = await request(app)
        .get('/api/currency/balance')
        .set('Authorization', `Bearer ${token}`);

      expect(response.status).toBe(404);
      expect(response.body.error).toContain('not available');
    });

    it('should deny currency access in dev branch', async () => {
      process.env.BRANCH_MODE = 'dev';
      const response = await request(app)
        .get('/api/currency/balance')
        .set('Authorization', `Bearer ${token}`);

      expect(response.status).toBe(404);
    });

    it('should allow currency access in devgame branch', async () => {
      process.env.BRANCH_MODE = 'devgame';
      const response = await request(app)
        .get('/api/currency/balance')
        .set('Authorization', `Bearer ${token}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('coins');
      expect(response.body.data).toHaveProperty('gems');
    });
  });
});
```

## 📝 Next Steps

1. **Implement remaining currency endpoints** (POST /spend, POST /earn)
2. **Add currency transaction history** (GET /transactions)
3. **Integrate with quiz completion rewards**
4. **Add admin currency management tools**
5. **Implement currency-based features** (shop, upgrades)

## � Complete API Endpoints

### Currency Balance Management

#### GET /api/currency/balance
**Description**: Lấy số dư hiện tại của user
**Branch**: Chỉ hoạt động ở `devgame`

```javascript
// Request
GET /api/currency/balance
Authorization: Bearer <token>

// Response (devgame)
{
  "success": true,
  "data": {
    "coins": 150,
    "gems": 12,
    "last_updated": "2025-01-15T10:30:00Z"
  },
  "branch": "devgame",
  "environment": "practice"
}

// Response (main/dev)
{
  "error": "Currency system not available",
  "branch": "main",
  "message": "Currency features are only available in practice mode (devgame)",
  "availableIn": ["devgame"]
}
```

#### POST /api/currency/earn
**Description**: Thêm tiền cho user (từ quiz completion, daily login, etc.)
**Branch**: Chỉ hoạt động ở `devgame`

```javascript
// Request
POST /api/currency/earn
Authorization: Bearer <token>
Content-Type: application/json

{
  "currency_type": "coins",
  "amount": 25,
  "source": "quiz_completion",
  "description": "Completed quiz: Basic Programming"
}

// Response
{
  "success": true,
  "data": {
    "transaction_id": 123,
    "currency_type": "coins",
    "amount_earned": 25,
    "balance_before": 150,
    "balance_after": 175,
    "source": "quiz_completion"
  },
  "message": "Currency earned successfully"
}
```

#### POST /api/currency/spend
**Description**: Trừ tiền của user (mua items, skills, etc.)
**Branch**: Chỉ hoạt động ở `devgame`

```javascript
// Request
POST /api/currency/spend
Authorization: Bearer <token>
Content-Type: application/json

{
  "currency_type": "gems",
  "amount": 5,
  "source": "skill_purchase",
  "description": "Purchased skill: Time Freeze"
}

// Response
{
  "success": true,
  "data": {
    "transaction_id": 124,
    "currency_type": "gems",
    "amount_spent": 5,
    "balance_before": 12,
    "balance_after": 7,
    "source": "skill_purchase"
  },
  "message": "Currency spent successfully"
}

// Error - Insufficient funds
{
  "success": false,
  "error": "Insufficient gems",
  "data": {
    "required": 10,
    "available": 7,
    "currency_type": "gems"
  }
}
```

#### GET /api/currency/transactions
**Description**: Lấy lịch sử giao dịch của user
**Branch**: Chỉ hoạt động ở `devgame`

```javascript
// Request
GET /api/currency/transactions?limit=20&offset=0&currency_type=coins
Authorization: Bearer <token>

// Response
{
  "success": true,
  "data": {
    "transactions": [
      {
        "id": 124,
        "transaction_type": "spend",
        "currency_type": "gems",
        "amount": -5,
        "balance_before": 12,
        "balance_after": 7,
        "source": "skill_purchase",
        "description": "Purchased skill: Time Freeze",
        "created_at": "2025-01-15T10:35:00Z"
      },
      {
        "id": 123,
        "transaction_type": "earn",
        "currency_type": "coins",
        "amount": 25,
        "balance_before": 150,
        "balance_after": 175,
        "source": "quiz_completion",
        "description": "Completed quiz: Basic Programming",
        "created_at": "2025-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "total": 45,
      "limit": 20,
      "offset": 0,
      "has_more": true
    }
  }
}
```

## 🔧 Advanced Implementation Details

### Currency Service Layer
```javascript
// backend/src/services/CurrencyService.js
const UserCurrency = require('../models/UserCurrency');
const CurrencyTransaction = require('../models/CurrencyTransaction');
const { getCurrentBranchConfig } = require('../config/branch');

class CurrencyService {
  static async getBalance(userId) {
    const branchConfig = getCurrentBranchConfig();
    if (!branchConfig.enableCurrency) {
      throw new Error('Currency system not available in this environment');
    }

    let balance = await UserCurrency.findOne({
      where: { user_id: userId, branch_mode: 'devgame' }
    });

    if (!balance) {
      balance = await this.createInitialBalance(userId);
    }

    return balance;
  }

  static async createInitialBalance(userId) {
    const { currencyConfig } = getCurrentBranchConfig();

    return await UserCurrency.create({
      user_id: userId,
      coins: currencyConfig.startingCoins,
      gems: currencyConfig.startingGems,
      branch_mode: 'devgame'
    });
  }

  static async earnCurrency(userId, currencyType, amount, source, description) {
    const transaction = await sequelize.transaction();

    try {
      const balance = await this.getBalance(userId);
      const balanceBefore = balance[currencyType];
      const balanceAfter = balanceBefore + amount;

      // Update balance
      await balance.update({
        [currencyType]: balanceAfter,
        updated_at: new Date()
      }, { transaction });

      // Record transaction
      const transactionRecord = await CurrencyTransaction.create({
        user_id: userId,
        transaction_type: 'earn',
        currency_type: currencyType,
        amount: amount,
        balance_before: balanceBefore,
        balance_after: balanceAfter,
        source: source,
        description: description,
        branch_mode: 'devgame'
      }, { transaction });

      await transaction.commit();
      return transactionRecord;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  static async spendCurrency(userId, currencyType, amount, source, description) {
    const transaction = await sequelize.transaction();

    try {
      const balance = await this.getBalance(userId);
      const balanceBefore = balance[currencyType];

      if (balanceBefore < amount) {
        throw new Error(`Insufficient ${currencyType}. Required: ${amount}, Available: ${balanceBefore}`);
      }

      const balanceAfter = balanceBefore - amount;

      // Update balance
      await balance.update({
        [currencyType]: balanceAfter,
        updated_at: new Date()
      }, { transaction });

      // Record transaction
      const transactionRecord = await CurrencyTransaction.create({
        user_id: userId,
        transaction_type: 'spend',
        currency_type: currencyType,
        amount: -amount, // Negative for spending
        balance_before: balanceBefore,
        balance_after: balanceAfter,
        source: source,
        description: description,
        branch_mode: 'devgame'
      }, { transaction });

      await transaction.commit();
      return transactionRecord;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  static async getTransactionHistory(userId, options = {}) {
    const {
      limit = 20,
      offset = 0,
      currency_type = null,
      transaction_type = null
    } = options;

    const whereClause = {
      user_id: userId,
      branch_mode: 'devgame'
    };

    if (currency_type) whereClause.currency_type = currency_type;
    if (transaction_type) whereClause.transaction_type = transaction_type;

    const { count, rows } = await CurrencyTransaction.findAndCountAll({
      where: whereClause,
      order: [['created_at', 'DESC']],
      limit,
      offset
    });

    return {
      transactions: rows,
      pagination: {
        total: count,
        limit,
        offset,
        has_more: offset + limit < count
      }
    };
  }
}

module.exports = CurrencyService;
```

### Quiz Integration
```javascript
// backend/src/controllers/quizController.js
const CurrencyService = require('../services/CurrencyService');
const { getCurrentBranchConfig } = require('../config/branch');

// Existing quiz completion handler
router.post('/complete', async (req, res) => {
  try {
    // ... existing quiz completion logic ...

    // Award currency if in devgame environment
    const branchConfig = getCurrentBranchConfig();
    if (branchConfig.enableCurrency) {
      const { quizCompletionReward } = branchConfig.currencyConfig;

      // Award coins
      await CurrencyService.earnCurrency(
        req.user.id,
        'coins',
        quizCompletionReward.coins,
        'quiz_completion',
        `Completed quiz: ${quiz.title}`
      );

      // Award gems for high scores
      if (score >= 80) {
        await CurrencyService.earnCurrency(
          req.user.id,
          'gems',
          quizCompletionReward.gems,
          'quiz_completion',
          `High score bonus for quiz: ${quiz.title}`
        );
      }
    }

    res.json({
      success: true,
      data: {
        score,
        // ... other quiz data ...
        currency_awarded: branchConfig.enableCurrency ? {
          coins: quizCompletionReward.coins,
          gems: score >= 80 ? quizCompletionReward.gems : 0
        } : null
      }
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
```

## 🧪 Comprehensive Testing

### Unit Tests
```javascript
// tests/services/CurrencyService.test.js
const CurrencyService = require('../../src/services/CurrencyService');
const { setupTestDB, cleanupTestDB } = require('../helpers/database');

describe('CurrencyService', () => {
  beforeAll(async () => {
    await setupTestDB();
    process.env.BRANCH_MODE = 'devgame';
  });

  afterAll(async () => {
    await cleanupTestDB();
  });

  describe('getBalance', () => {
    it('should create initial balance for new user', async () => {
      const userId = 1;
      const balance = await CurrencyService.getBalance(userId);

      expect(balance.coins).toBe(100); // Starting coins
      expect(balance.gems).toBe(10);   // Starting gems
      expect(balance.branch_mode).toBe('devgame');
    });

    it('should return existing balance', async () => {
      const userId = 1;
      const balance1 = await CurrencyService.getBalance(userId);
      const balance2 = await CurrencyService.getBalance(userId);

      expect(balance1.id).toBe(balance2.id);
    });
  });

  describe('earnCurrency', () => {
    it('should add coins correctly', async () => {
      const userId = 1;
      const transaction = await CurrencyService.earnCurrency(
        userId, 'coins', 25, 'quiz_completion', 'Test quiz'
      );

      expect(transaction.amount).toBe(25);
      expect(transaction.balance_after).toBe(125); // 100 + 25

      const balance = await CurrencyService.getBalance(userId);
      expect(balance.coins).toBe(125);
    });
  });

  describe('spendCurrency', () => {
    it('should deduct coins correctly', async () => {
      const userId = 1;
      const transaction = await CurrencyService.spendCurrency(
        userId, 'coins', 50, 'skill_purchase', 'Test skill'
      );

      expect(transaction.amount).toBe(-50);
      expect(transaction.balance_after).toBe(75); // 125 - 50
    });

    it('should throw error for insufficient funds', async () => {
      const userId = 1;

      await expect(
        CurrencyService.spendCurrency(userId, 'coins', 1000, 'test', 'test')
      ).rejects.toThrow('Insufficient coins');
    });
  });
});
```

### Integration Tests
```javascript
// tests/integration/currency.integration.test.js
const request = require('supertest');
const app = require('../../src/app');

describe('Currency API Integration', () => {
  let authToken;

  beforeAll(async () => {
    // Setup test user and get auth token
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({ email: '<EMAIL>', password: 'password' });

    authToken = loginResponse.body.token;
  });

  describe('Environment Protection', () => {
    it('should block currency access in main branch', async () => {
      process.env.BRANCH_MODE = 'main';

      const response = await request(app)
        .get('/api/currency/balance')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(404);
      expect(response.body.error).toContain('not available');
    });

    it('should allow currency access in devgame branch', async () => {
      process.env.BRANCH_MODE = 'devgame';

      const response = await request(app)
        .get('/api/currency/balance')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('coins');
      expect(response.body.data).toHaveProperty('gems');
    });
  });

  describe('Currency Operations', () => {
    beforeEach(() => {
      process.env.BRANCH_MODE = 'devgame';
    });

    it('should complete full currency flow', async () => {
      // Get initial balance
      const balanceResponse = await request(app)
        .get('/api/currency/balance')
        .set('Authorization', `Bearer ${authToken}`);

      const initialCoins = balanceResponse.body.data.coins;

      // Earn currency
      const earnResponse = await request(app)
        .post('/api/currency/earn')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          currency_type: 'coins',
          amount: 50,
          source: 'test',
          description: 'Test earning'
        });

      expect(earnResponse.status).toBe(200);
      expect(earnResponse.body.data.balance_after).toBe(initialCoins + 50);

      // Spend currency
      const spendResponse = await request(app)
        .post('/api/currency/spend')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          currency_type: 'coins',
          amount: 25,
          source: 'test',
          description: 'Test spending'
        });

      expect(spendResponse.status).toBe(200);
      expect(spendResponse.body.data.balance_after).toBe(initialCoins + 25);

      // Check transaction history
      const historyResponse = await request(app)
        .get('/api/currency/transactions')
        .set('Authorization', `Bearer ${authToken}`);

      expect(historyResponse.status).toBe(200);
      expect(historyResponse.body.data.transactions).toHaveLength(2);
    });
  });
});
```

## 📊 Monitoring & Analytics

### Currency Analytics Dashboard
```javascript
// backend/src/controllers/currencyAnalyticsController.js
router.get('/analytics/overview', async (req, res) => {
  try {
    const analytics = await sequelize.query(`
      SELECT
        COUNT(DISTINCT user_id) as total_users_with_currency,
        SUM(coins) as total_coins_in_circulation,
        SUM(gems) as total_gems_in_circulation,
        AVG(coins) as avg_coins_per_user,
        AVG(gems) as avg_gems_per_user
      FROM user_currency
      WHERE branch_mode = 'devgame'
    `, { type: QueryTypes.SELECT });

    res.json({
      success: true,
      data: {
        overview: analytics[0],
        branch: 'devgame',
        period: 'last_30_days'
      }
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
```

## 🔄 Migration Strategy

### Database Migration Script
```sql
-- migrations/add_branch_mode_to_existing_tables.sql
-- Step 1: Add branch_mode column to existing tables
ALTER TABLE users ADD COLUMN IF NOT EXISTS branch_mode VARCHAR(20) DEFAULT 'dev';
ALTER TABLE quiz_attempts ADD COLUMN IF NOT EXISTS branch_mode VARCHAR(20) DEFAULT 'dev';

-- Step 2: Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_branch_mode ON users(branch_mode);
CREATE INDEX IF NOT EXISTS idx_quiz_attempts_branch_mode ON quiz_attempts(branch_mode);

-- Step 3: Update existing data
UPDATE users SET branch_mode = 'dev' WHERE branch_mode IS NULL;
UPDATE quiz_attempts SET branch_mode = 'dev' WHERE branch_mode IS NULL;

-- Step 4: Create currency tables
CREATE TABLE IF NOT EXISTS user_currency (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    coins INTEGER DEFAULT 0 CHECK (coins >= 0),
    gems INTEGER DEFAULT 0 CHECK (gems >= 0),
    branch_mode VARCHAR(20) DEFAULT 'devgame' CHECK (branch_mode = 'devgame'),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_id, branch_mode)
);
```

## 🎯 Implementation Checklist

### Phase 1: Infrastructure Setup
- [ ] Update Docker Compose configuration
- [ ] Add environment variables for branch modes
- [ ] Create branch configuration files
- [ ] Set up NGINX routing for multiple environments

### Phase 2: Database Setup
- [ ] Run database migration scripts
- [ ] Create currency tables with branch constraints
- [ ] Add indexes for performance
- [ ] Verify data isolation between branches

### Phase 3: Backend Implementation
- [ ] Implement branch configuration system
- [ ] Create currency guard middleware
- [ ] Implement CurrencyService with branch awareness
- [ ] Add currency routes with protection
- [ ] Integrate currency rewards with quiz completion

### Phase 4: Testing
- [ ] Write unit tests for CurrencyService
- [ ] Create integration tests for API endpoints
- [ ] Test branch isolation (currency not available in main/dev)
- [ ] Performance testing for currency operations

### Phase 5: Deployment
- [ ] Deploy to staging environment
- [ ] Test all three environments (main, dev, devgame)
- [ ] Verify currency system only works in devgame
- [ ] Monitor performance and error rates

## 📞 Support & Troubleshooting

### Common Issues

1. **Currency API returns 404 in devgame**
   - Check `BRANCH_MODE` environment variable
   - Verify currency guard middleware is not blocking requests

2. **Database connection issues**
   - Verify all environments use same database
   - Check branch_mode column exists in tables

### Debug Commands
```bash
# Check environment variables
docker exec ql_ctdt_game_backend env | grep BRANCH

# Test currency API
curl -X GET http://localhost:8890/api/currency/balance \
  -H "Authorization: Bearer YOUR_TOKEN"

# Monitor logs
docker-compose -f docker-compose.multi-env.yml logs -f game-backend
```
## �🔗 Related Documents

- `doc_for_devgame/task2/TASK_2_1_CURRENCY_SYSTEM_GUIDE.md` - Original currency implementation
- `doc_for_devgame/COMPLETE_GAMIFICATION_SYSTEM_GUIDE.md` - Full gamification overview
- `backend/src/routes/currencyRoutes.js` - Current currency API implementation
- `DEPLOYMENT_GUIDE.md` - Complete deployment instructions
