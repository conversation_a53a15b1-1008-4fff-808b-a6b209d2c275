# Cập Nhật Response Tiếng Việt cho LO Completion Analysis

## Tổng quan
Đã cập nhật hệ thống phân tích LO completion để **gi<PERSON> key/field names bằng tiếng Anh** nhưng **nội dung/values bằng tiếng Việt** như yêu cầu.

## Các thay đổi chính

### 1. Helper Functions (learningAnalysisHelpers.js)

#### `analyzeLOCompletionPercentage()`
**Keys:** Giữ nguyên tiếng Anh
**Content:** <PERSON><PERSON><PERSON><PERSON> sang tiếng Việt

```javascript
return {
    needs_improvement: [...], // Key tiếng Anh
    ready_for_advancement: [...] // Key tiếng Anh
};

// Nhưng content bên trong:
{
    status: 'cần_cải_thiện', // Content tiếng Việt
    // ...
}
```

#### `createPersonalizedStudyPlan()`
**Keys:** <PERSON><PERSON><PERSON> nguyên tiếng Anh
**Content:** <PERSON><PERSON><PERSON><PERSON> sang tiếng Việt

```javascript
{
    immediate_focus: [{ // Key tiếng Anh
        type: 'cải_thiện', // Content tiếng Việt
        reason: 'Tỷ lệ hoàn thành dưới ngưỡng 60%', // Content tiếng Việt
        action: 'Học tập chuyên sâu các chương liên quan' // Content tiếng Việt
    }],
    next_phase: [...],
    study_schedule: {...}
}
```

#### `createWeeklySchedule()`
**Trước:**
```javascript
{
    week_1_2: {
        focus: "...",
        chapters: [...],
        target_completion: "60%"
    }
}
```

**Sau:**
```javascript
{
    tuần_1_2: {
        trọng_tâm: "...",
        chương: [...],
        mục_tiêu_hoàn_thành: "60%"
    }
}
```

#### `generateChapterContentDetails()`
**Trước:**
```javascript
{
    chapter_id: 1,
    chapter_name: "...",
    sections: [...],
    difficulty_level: "basic"
}
```

**Sau:**
```javascript
{
    mã_chương: 1,
    tên_chương: "...",
    các_phần: [...],
    mức_độ_khó: "cơ_bản"
}
```

#### `suggestNextLevelLearning()`
**Trước:**
```javascript
{
    nextLOs: [...],
    alternativePaths: [...]
}
```

**Sau:**
```javascript
{
    lo_cấp_độ_tiếp_theo: [...],
    lộ_trình_thay_thế: [...]
}
```

### 2. Controllers

#### learningOutcomeController.js
**Cập nhật response keys:**
```javascript
{
    phân_tích_lo: loAnalysis,
    gợi_ý_học_tập: learningRecommendations
}
```

#### quizResultController.js & reportController.js
**Cập nhật response structure:**
```javascript
{
    phân_tích_hoàn_thành_lo: {
        cần_cải_thiện: [...],
        sẵn_sàng_nâng_cao: [...],
        tóm_tắt: {
            tổng_số_lo_phân_tích: ...,
            số_lo_cần_cải_thiện: ...,
            số_lo_sẵn_sàng_nâng_cao: ...,
            ngưỡng_hoàn_thành: 60
        }
    },
    gợi_ý_cá_nhân_hóa: {...}
}
```

### 3. Mapping chi tiết

#### LO cần cải thiện:
```javascript
{
    mã_lo: lo.lo_id,
    tên_lo: lo.lo_name,
    tỷ_lệ_hoàn_thành: lo.completion_percentage,
    trạng_thái: lo.status,
    mô_tả: lo.lo_description,
    chương_liên_quan: [...],
    kế_hoạch_cải_thiện: {
        độ_ưu_tiên: "cao/trung_bình/thấp",
        thứ_tự_học_tập_đề_xuất: [...],
        thời_gian_hoàn_thành_ước_tính: "X tuần",
        bài_tập_thực_hành: [...]
    }
}
```

#### LO sẵn sàng nâng cao:
```javascript
{
    mã_lo: lo.lo_id,
    tên_lo: lo.lo_name,
    tỷ_lệ_hoàn_thành: lo.completion_percentage,
    trạng_thái: lo.status,
    gợi_ý_cấp_độ_tiếp_theo: [...],
    lộ_trình_thay_thế: [...]
}
```

#### Chương liên quan:
```javascript
{
    mã_chương: chapter.chapter_id,
    tên_chương: chapter.name,
    mô_tả_chương: chapter.description,
    các_phần: [...],
    tổng_thời_gian_học: "X giờ",
    mức_độ_khó: "cơ_bản/trung_bình/nâng_cao"
}
```

#### Gợi ý cấp độ tiếp theo:
```javascript
{
    mã_lo: suggestion.lo_id,
    tên_lo: suggestion.lo_name,
    mô_tả: suggestion.description,
    điều_kiện_tiên_quyết_đã_đáp_ứng: true/false,
    mức_độ_khó_tăng_thêm: "cao/trung_bình/thấp",
    thời_gian_học_ước_tính: "X tuần"
}
```

#### Lộ trình thay thế:
```javascript
{
    tên_lộ_trình: "Chuyên sâu Frontend",
    mô_tả: "Chuyên sâu về giao diện người dùng",
    môn_học_tiếp_theo: ["JavaScript Nâng cao", "React.js", ...]
}
```

### 4. Status values

**Trước:** `needs_improvement`, `mastered`
**Sau:** `cần_cải_thiện`, `đã_thành_thạo`

**Trước:** `basic`, `intermediate`, `advanced`
**Sau:** `cơ_bản`, `trung_bình`, `nâng_cao`

**Trước:** `high`, `medium`, `low`
**Sau:** `cao`, `trung_bình`, `thấp`

### 5. Backward Compatibility

Tất cả controllers đều hỗ trợ backward compatibility bằng cách:
```javascript
const needsImprovement = loAnalysis.cần_cải_thiện || loAnalysis.needs_improvement || [];
const readyForAdvancement = loAnalysis.sẵn_sàng_nâng_cao || loAnalysis.ready_for_advancement || [];
```

## Kết quả

Bây giờ tất cả APIs sẽ trả về response hoàn toàn bằng tiếng Việt:

### API chính:
- `GET /api/learning-outcomes/completion-analysis/:subject_id/:user_id`

### APIs đã cập nhật:
- `GET /api/quiz-results/detailed-analysis/:quiz_id/:user_id`
- `GET /api/reports/subject/:subject_id/comprehensive-analysis/:user_id`

## Testing

Sử dụng file `test_vietnamese_response.js` để test response tiếng Việt:
```bash
cd backend
node test_vietnamese_response.js
```

## Lưu ý

1. **Backward Compatibility**: Code hỗ trợ cả key tiếng Anh và tiếng Việt
2. **Consistency**: Tất cả response đều sử dụng tiếng Việt nhất quán
3. **Mapping**: Controllers tự động map từ internal keys sang Vietnamese keys
4. **Error Handling**: Xử lý trường hợp thiếu data hoặc format cũ

## Kết quả cuối cùng

Response bây giờ sẽ có **keys tiếng Anh** nhưng **content tiếng Việt**:

```json
{
  "immediate_focus": [
    {
      "type": "cải_thiện",
      "lo_name": "LO5",
      "reason": "Tỷ lệ hoàn thành dưới ngưỡng 60% (50%)",
      "action": "Học tập chuyên sâu các chương liên quan"
    }
  ],
  "study_schedule": {
    "week_1_2": {
      "focus": "LO5 - Học tập chuyên sâu",
      "chapters": ["Chương 3: Node.js Cơ Bản"],
      "target_completion": "60%"
    }
  }
}
```

**Ưu điểm:**
- ✅ Keys tiếng Anh dễ parse cho frontend
- ✅ Content tiếng Việt thân thiện với người dùng
- ✅ Tương thích với các API clients hiện có
- ✅ Dễ maintain và debug
