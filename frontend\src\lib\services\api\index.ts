import api from "./client";
import authService from "./auth.service";
import userService from "./user.service";
import roleService from "./role.service";
import quizService from "./quiz.service";
import subjectService from "./subject.service";
import loService from "./lo.service";
import gamificationService from "./gamification.service";
import advancedAnalyticsService from "./advanced-analytics.service";
import chapterAnalyticsService from "./chapter-analytics.service";
import avatarService from "./avatar.service";
import currencyService from "./currency.service";

export {
  api,
  authService,
  userService,
  roleService,
  quizService,
  subjectService,
  loService,
  gamificationService,
  advancedAnalyticsService,
  chapterAnalyticsService,
  avatarService,
  currencyService,
};

export default api;

// Re-export types from services
export type {
  TimeSeriesParams,
  ScoreDistributionParams,
  LearningOutcomesParams,
  DifficultyHeatmapParams,
  ActivityTimelineParams,
  StudentScoreAnalysisParams,
} from "./advanced-analytics.service";

export type {
  UserGamificationInfo,
  LeaderboardEntry,
  GamificationStats,
  AddPointsRequest,
} from "@/lib/types/gamification";

// Chapter Analytics types
export type {
  ChapterAnalysisData,
  ComprehensiveAnalysisData,
  TeacherAnalyticsData,
  ChapterAnalyticsResponse,
  DetailedAnalysisParams,
  ComprehensiveAnalysisParams,
  TeacherAnalyticsParams,
  SectionRecommendation,
} from "@/lib/types/chapter-analytics";

// Currency types
export type {
  CurrencyBalance,
  CurrencyData,
  UserCurrencies,
  CurrencyApiResponse,
  CurrencyError,
  CurrencyDisplayConfig,
  CurrencyUpdateEvent,
} from "@/lib/types/currency";

// LO Completion Analysis types
export type {
  LOResponse,
  LOPaginatedResponse,
  LOsBySubjectResponse,
  LOCompletionAnalysisParams,
  LOAnalysisItem,
  PersonalizedRecommendations,
  LOCompletionAnalysisResponse,
  LODetailsResponse,
} from "./lo.service";

// Additional LO types from dedicated types file
export type {
  LOImprovementPlan,
  LONextLevelSuggestion,
  LOCompletionAnalysisData,
  ActualNextPhaseItem,
  ActualStudySchedule,
} from "@/lib/types/lo-completion-analysis";
