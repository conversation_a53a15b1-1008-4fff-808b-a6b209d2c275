/**
 * TEST SCRIPT FOR VIETNAMESE RESPONSE
 * Script để test response tiếng Việt
 */

const {
    analyzeLOCompletionPercentage,
    createPersonalizedStudyPlan
} = require('./src/utils/learningAnalysisHelpers');

// Mock data để test
const mockQuestionHistory = [
    {
        Question: {
            LO: {
                lo_id: 1,
                name: 'LO1 - HTML/CSS Cơ bản',
                description: 'Học HTML và CSS cơ bản'
            }
        },
        is_correct: true,
        time_spent: 30
    },
    {
        Question: {
            LO: {
                lo_id: 1,
                name: 'LO1 - HTML/CSS Cơ bản',
                description: 'Học HTML và CSS cơ bản'
            }
        },
        is_correct: true,
        time_spent: 25
    },
    {
        Question: {
            LO: {
                lo_id: 1,
                name: 'LO1 - HTML/CSS Cơ bản',
                description: 'Học HTML và CSS cơ bản'
            }
        },
        is_correct: false,
        time_spent: 45
    },
    {
        Question: {
            LO: {
                lo_id: 2,
                name: 'LO2 - JavaScript Cơ bản',
                description: 'Học JavaScript cơ bản'
            }
        },
        is_correct: false,
        time_spent: 60
    },
    {
        Question: {
            LO: {
                lo_id: 2,
                name: 'LO2 - JavaScript Cơ bản',
                description: 'Học JavaScript cơ bản'
            }
        },
        is_correct: false,
        time_spent: 50
    }
];

async function testVietnameseResponse() {
    try {
        console.log('🧪 Testing Vietnamese Response...\n');
        
        // Test phân tích LO completion
        const loAnalysis = await analyzeLOCompletionPercentage(mockQuestionHistory, 1, 60);
        
        console.log('📊 LO Analysis Result:');
        console.log('Keys:', Object.keys(loAnalysis));
        
        if (loAnalysis.needs_improvement) {
            console.log('\n✅ Found "needs_improvement" key (English key)');
            console.log('LOs needing improvement:', loAnalysis.needs_improvement.length);

            if (loAnalysis.needs_improvement.length > 0) {
                const firstLO = loAnalysis.needs_improvement[0];
                console.log('First LO status (Vietnamese content):', firstLO.status);
                console.log('First LO keys:', Object.keys(firstLO));
            }
        }

        if (loAnalysis.ready_for_advancement) {
            console.log('\n✅ Found "ready_for_advancement" key (English key)');
            console.log('LOs ready for advancement:', loAnalysis.ready_for_advancement.length);

            if (loAnalysis.ready_for_advancement.length > 0) {
                const firstLO = loAnalysis.ready_for_advancement[0];
                console.log('First LO status (Vietnamese content):', firstLO.status);
                console.log('First LO keys:', Object.keys(firstLO));
            }
        }
        
        // Test personalized study plan
        const studyPlan = createPersonalizedStudyPlan(loAnalysis);
        
        console.log('\n📚 Study Plan Result:');
        console.log('Keys:', Object.keys(studyPlan));
        
        if (studyPlan.immediate_focus) {
            console.log('\n✅ Found "immediate_focus" key (English key)');
            console.log('Immediate focus items:', studyPlan.immediate_focus.length);

            if (studyPlan.immediate_focus.length > 0) {
                const firstItem = studyPlan.immediate_focus[0];
                console.log('First item keys:', Object.keys(firstItem));
                console.log('First item type (Vietnamese content):', firstItem.type);
                console.log('First item reason (Vietnamese content):', firstItem.reason);
            }
        }

        if (studyPlan.study_schedule) {
            console.log('\n✅ Found "study_schedule" key (English key)');
            console.log('Schedule keys:', Object.keys(studyPlan.study_schedule));

            const scheduleKeys = Object.keys(studyPlan.study_schedule);
            if (scheduleKeys.length > 0) {
                const firstSchedule = studyPlan.study_schedule[scheduleKeys[0]];
                if (firstSchedule) {
                    console.log('First schedule keys:', Object.keys(firstSchedule));
                    console.log('Focus (Vietnamese content):', firstSchedule.focus);
                    console.log('Target (Vietnamese content):', firstSchedule.target_completion);
                }
            }
        }
        
        console.log('\n🎉 Vietnamese response test completed successfully!');
        
        return {
            loAnalysis,
            studyPlan
        };
        
    } catch (error) {
        console.error('❌ Error testing Vietnamese response:', error.message);
        console.error('Stack:', error.stack);
        return null;
    }
}

// Chạy test nếu file được execute trực tiếp
if (require.main === module) {
    testVietnameseResponse().catch(console.error);
}

module.exports = {
    testVietnameseResponse
};
