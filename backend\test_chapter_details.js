/**
 * TEST SCRIPT FOR CHAPTER CONTENT DETAILS
 * Script để test function generateChapterContentDetails
 */

const { generateChapterContentDetails } = require('./src/utils/learningAnalysisHelpers');

async function testChapterContentDetails() {
    try {
        console.log('🧪 Testing generateChapterContentDetails function...\n');
        
        // Test với LO ID = 1 (thay đổi theo dữ liệu thực tế)
        const loId = 1;
        
        console.log(`Testing with LO ID: ${loId}`);
        
        const chapterDetails = await generateChapterContentDetails(loId);
        
        console.log('✅ Function executed successfully!');
        console.log('📊 Results:');
        console.log(`- Number of chapters found: ${chapterDetails.length}`);
        
        if (chapterDetails.length > 0) {
            const firstChapter = chapterDetails[0];
            console.log('\n📖 First chapter details:');
            console.log(`- Chapter ID: ${firstChapter.chapter_id}`);
            console.log(`- Chapter Name: ${firstChapter.chapter_name}`);
            console.log(`- Chapter Description: ${firstChapter.chapter_description}`);
            console.log(`- Number of sections: ${firstChapter.sections?.length || 0}`);

            if (firstChapter.sections && firstChapter.sections.length > 0) {
                const firstSection = firstChapter.sections[0];
                console.log('\n📝 First section details:');
                console.log(`- Section ID: ${firstSection.section_id}`);
                console.log(`- Title: ${firstSection.title}`);
                console.log(`- Content: ${firstSection.content ? firstSection.content.substring(0, 100) + '...' : 'Không có nội dung'}`);
            }
        } else {
            console.log('⚠️ No chapters found for this LO');
        }
        
        return chapterDetails;
        
    } catch (error) {
        console.error('❌ Error testing generateChapterContentDetails:', error.message);
        console.error('Stack:', error.stack);
        return null;
    }
}

// Test với nhiều LO IDs
async function testMultipleLOs() {
    console.log('\n🔄 Testing with multiple LO IDs...\n');
    
    const testLOIds = [1, 2, 3, 4, 5]; // Thay đổi theo dữ liệu thực tế
    
    for (const loId of testLOIds) {
        console.log(`\n--- Testing LO ID: ${loId} ---`);
        try {
            const result = await generateChapterContentDetails(loId);
            console.log(`✅ LO ${loId}: Found ${result.length} chapters`);
        } catch (error) {
            console.log(`❌ LO ${loId}: Error - ${error.message}`);
        }
    }
}

// Chạy tests nếu file được execute trực tiếp
if (require.main === module) {
    testChapterContentDetails()
        .then(() => testMultipleLOs())
        .catch(console.error);
}

module.exports = {
    testChapterContentDetails,
    testMultipleLOs
};
